{"author": "", "dependencies": {"@hapi/joi": "^17.1.1", "@malou-io/package-utils": "workspace:*", "bcryptjs": "^3.0.2", "json-schema-to-ts": "^2.6.0", "jsonwebtoken": "^9.0.0", "luxon": "^1.25.0", "mongodb": "6.16.0", "mongoose": "^8.14.1", "pluralize": "^8.0.0", "ts-jest": "^29.4.0", "uuid": "^9.0.1", "zod": "^3.21.4"}, "description": "", "devDependencies": {"@babel/preset-typescript": "^7.17.12", "@malou-io/package-config": "workspace:*", "@sentry/cli": "^2.32.1", "@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/bcrypt": "^5.0.2", "@types/eslint": "^8.48.0", "@types/glob": "^8.1.0", "@types/jest": "^29.0.0", "@types/jsonwebtoken": "9", "@types/luxon": "^1.27.1", "@types/node": "^16.3.1", "@types/pluralize": "^0.0.33", "@types/uuid": "^9.0.7", "@types/yargs": "^17.0.32", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.48.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.28.1", "glob": "^10.3.12", "jest": "^29.0.0", "mongodb-memory-server": "^9.3.0", "pluralize": "^8.0.0", "prettier": "^3.5.3", "quicktype": "^23.0.80", "ts-toolbelt": "^9.6.0", "tslib": "^2.6.2", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "typescript": "^5.3.3", "utility-types": "^3.11.0"}, "license": "ISC", "main": "./lib/index.js", "name": "@malou-io/package-models", "private": true, "scripts": {"build": "tsc && tsc-alias", "build-clean": "rm -rf ./lib && rm -rf .turbo && rm -f tsconfig.tsbuildinfo", "build-development": "tsc && tsc-alias", "build-production": "tsc && tsc-alias", "build-staging": "tsc && tsc-alias", "create-json-schema-from-samples": "ts-node ./src/core/mongoose-json-schema/scripts/create-json-schema-from-samples.ts", "debug-schema": "ts-node ./src/core/mongoose-json-schema/scripts/debug-schema.ts", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "format:check": "prettier \"**/*.{ts,tsx,md}\" --check", "generate-model-file": "ts-node ./src/core/mongoose-json-schema/scripts/generate-model-file.ts", "generate-ts-json-schema": "ts-node ./src/core/mongoose-json-schema/scripts/generate-ts-json-schema.ts", "lint": "eslint \"**/*.ts\"", "lint-fix": "eslint \"**/*.ts\" --fix", "lint-staged": "lint-staged --no-stash", "preinstall": "npx only-allow pnpm", "quicktype": "quicktype", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org ${SENTRY_ORG} --project ${SENTRY_PROJECT} --release ${SENTRY_RELEASE} ./lib && sentry-cli sourcemaps upload --org ${SENTRY_ORG} --project ${SENTRY_PROJECT} --release ${SENTRY_RELEASE} ./lib", "start-dev": "pnpm run update-ts-paths-and-watch", "start-local": "pnpm run update-ts-paths-and-watch", "start-production": "pnpm run update-ts-paths-and-watch", "start-staging": "pnpm run update-ts-paths-and-watch", "test": "jest --config jest.config.unit.ts", "test:unit": "jest unit -c jest.config.unit.ts --force-exit --color", "test:unit:single": "jest -c jest.config.unit.ts --watch", "update-ts-paths-and-watch": "tsc-alias -w"}, "types": "./lib/index.d.ts", "version": "0.0.0"}