import { FromSchema } from 'json-schema-to-ts';
import mongoose, { Query } from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';
import { generatePublicBusinessId } from ':helpers/generate-review-public-business-id';
import { DbId } from ':helpers/interfaces';
import { privateReviewJSONSchema } from ':modules/private-reviews/private-review-schema';

const privateReviewSchema = createMongooseSchemaFromJSONSchema(privateReviewJSONSchema);

privateReviewSchema.virtual('client', {
    ref: 'Client',
    localField: 'clientId',
    foreignField: '_id',
    justOne: true,
});

privateReviewSchema.virtual('restaurant', {
    ref: 'Restaurant',
    localField: 'restaurantId',
    foreignField: '_id',
    justOne: true,
});

privateReviewSchema.virtual('campaign', {
    ref: 'Campaign',
    localField: 'campaignId',
    foreignField: '_id',
    justOne: true,
});

privateReviewSchema.virtual('scan', {
    ref: 'Scans',
    localField: 'scanId',
    foreignField: '_id',
    justOne: true,
});

privateReviewSchema.virtual('translations', {
    ref: 'Translations',
    localField: 'translationsId',
    foreignField: '_id',
    justOne: true,
});

privateReviewSchema.index({ text: 'text', 'reviewer.displayName': 'text', 'comments.text': 'text' }); // index required by $search operator to search on review text: https://docs.mongodb.com/manual/text-search/
privateReviewSchema.index({ campaignId: 1, client: 1 });
privateReviewSchema.index({ scanId: 1 });
privateReviewSchema.index({ restaurantId: 1, socialSortDate: 1 });

privateReviewSchema.index({ publicBusinessId: 1 }, { unique: true });

privateReviewSchema.pre<IPrivateReview>('validate', async function () {
    if (this.publicBusinessId) {
        return;
    }

    this.publicBusinessId = await generatePublicBusinessId(this.restaurantId);
});

privateReviewSchema.pre<Query<any, IPrivateReview>>('findOneAndUpdate', async function () {
    const opts = this.getOptions();
    const update = this.getUpdate() as any;

    if ((opts.upsert && update.$setOnInsert?.publicBusinessId) || (!opts.upsert && update.$set?.publicBusinessId)) {
        return;
    }

    const filter = this.getQuery() as Record<string, any>;
    if (filter.publicBusinessId) {
        return;
    }

    const existing = await this.model.findOne<{ restaurantId: DbId }>(filter, { restaurantId: 1, publicBusinessId: 1 }).lean();
    if (existing?.publicBusinessId) {
        return;
    }

    const restaurantId = existing?.restaurantId || filter.restaurantId;
    if (!restaurantId) {
        return;
    }

    const publicBusinessId = await generatePublicBusinessId(restaurantId);
    if (!publicBusinessId) {
        return;
    }

    this.setQuery({
        ...filter,
        publicBusinessId: { $exists: false },
    });
    this.setUpdate({
        ...update,
        $set: { ...(update.$set || {}), publicBusinessId },
    });
});

export type IPrivateReview = FromSchema<
    typeof privateReviewJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const PrivateReviewModel = mongoose.model<IPrivateReview>(privateReviewJSONSchema.title, privateReviewSchema);
