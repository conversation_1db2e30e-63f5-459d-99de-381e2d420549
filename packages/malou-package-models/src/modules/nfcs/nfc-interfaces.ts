import { OverwriteOrAssign } from ':core/index';
import { IRestaurant } from ':modules/restaurants/restaurant-model';

import { INfc } from './nfc-model';

export type NfcRestaurant = Pick<
    IRestaurant,
    '_id' | 'name' | 'logo' | 'cover' | 'address' | 'active' | 'internalName' | 'totemDisplayName' | 'type' | 'boosterPack'
>;

export type INfcWithRestaurant = OverwriteOrAssign<INfc, { restaurant: NfcRestaurant }>;

export type ILightNfc = OverwriteOrAssign<
    Pick<INfc, '_id' | 'chipName' | 'restaurantId' | 'platformKey' | 'type'> & {
        /** The optional user-defined custom name, only for totems (not for stickers) */
        name?: string;
    },
    { restaurant?: Pick<NfcRestaurant, '_id' | 'name' | 'internalName'> }
>;
