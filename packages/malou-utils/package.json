{"dependencies": {"@casl/ability": "^5.4.3", "@ladjs/country-language": "^1.0.3", "bson-objectid": "^2.0.4", "libphonenumber-js": "^1.11.18", "lodash": "^4.17.21", "luxon": "^3.4.1", "neverthrow": "^8.1.1", "slugify": "^1.6.6", "tlds": "^1.255.0"}, "devDependencies": {"@malou-io/package-config": "workspace:*", "@sentry/cli": "^2.32.1", "@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/eslint": "^8.48.0", "@types/jest": "^28.1.8", "@types/lodash": "^4.17.0", "@types/luxon": "^3.0.0", "@types/node": "^16.3.1", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.48.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.28.1", "jest": "^28.1.3", "prettier": "^3.5.3", "ts-jest": "next", "tslib": "^2.6.2", "typescript": "^5.3.3"}, "main": "./lib/index.js", "name": "@malou-io/package-utils", "private": true, "scripts": {"build": "tsc", "build-clean": "rm -rf ./lib && rm -rf .turbo && rm -f tsconfig.tsbuildinfo", "build-development": "tsc", "build-production": "tsc", "build-staging": "tsc", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "format:check": "prettier \"**/*.{ts,tsx,md}\" --check", "lint": "eslint \"**/*.ts\"", "lint-fix": "eslint \"**/*.ts\" --fix", "lint-staged": "lint-staged --no-stash", "preinstall": "npx only-allow pnpm", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org ${SENTRY_ORG} --project ${SENTRY_PROJECT} --release ${SENTRY_RELEASE} ./lib && sentry-cli sourcemaps upload --org ${SENTRY_ORG} --project ${SENTRY_PROJECT} --release ${SENTRY_RELEASE} ./lib", "test": "jest -c jest.config.unit.ts --color", "test:unit": "jest -c jest.config.unit.ts --color", "test:unit:single": "jest -c jest.config.unit.ts --watch", "watch-build": "tsc -w"}, "types": "./lib/index.d.ts", "version": "0.0.0"}