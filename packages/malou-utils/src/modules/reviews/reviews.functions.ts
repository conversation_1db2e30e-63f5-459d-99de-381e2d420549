import slugify from 'slugify';

import { ReviewAnalysisSentiment, ReviewAnalysisStatus, ReviewAnalysisTag, SemanticAnalysisProviderKey } from '../../constants';
import { isNotNil } from '../../functions';
import { REVIEW_PUBLIC_BUSINESS_ID_SEPARATOR } from './constants';

// TODO: Remove when feature toggle 'release-new-semantic-analysis' is removed
export function getReviewsForSemanticAnalysis(reviews) {
    return reviews.filter(
        (review) =>
            review.semanticAnalysis?.providerKey === SemanticAnalysisProviderKey.OPENAI &&
            review.semanticAnalysis?.status !== ReviewAnalysisStatus.FAILED
    );
}

export function getSegmentsForSemanticAnalysisStats(reviews, isFeatureAvailableForRestaurant: boolean) {
    if (isFeatureAvailableForRestaurant) {
        return reviews.flatMap((review) =>
            review.semanticAnalysisSegments.filter(
                (segmentAnalyse) =>
                    segmentAnalyse.tag !== ReviewAnalysisTag.OVERALL_EXPERIENCE &&
                    segmentAnalyse.sentiment !== ReviewAnalysisSentiment.NEUTRAL
            )
        );
    }
    return reviews
        .flatMap((review) =>
            review.semanticAnalysis?.segmentAnalyses.map(({ tag, sentiment, segment }) => ({
                category: tag,
                sentiment,
                segment,
            }))
        )
        .filter(isNotNil)
        .filter(
            (segmentAnalyse) =>
                segmentAnalyse.category !== ReviewAnalysisTag.OVERALL_EXPERIENCE &&
                segmentAnalyse.sentiment !== ReviewAnalysisSentiment.NEUTRAL
        );
}

export function getPositiveAndNegativeStatsForSemanticAnalysis(segments: { sentiment: string }[]) {
    const positiveSentimentsCount = segments.filter((s) => s.sentiment === ReviewAnalysisSentiment.POSITIVE).length;
    const negativeSentimentsCount = segments.filter((s) => s.sentiment === ReviewAnalysisSentiment.NEGATIVE).length;
    const positiveSentimentsPercentage = Math.round((positiveSentimentsCount * 100) / segments.length);
    const negativeSentimentsPercentage = Math.round((negativeSentimentsCount * 100) / segments.length);

    return {
        positiveSentimentsCount,
        positiveSentimentsPercentage,

        negativeSentimentsCount,
        negativeSentimentsPercentage,
    };
}

export function getNumberFromPublicBusinessId(publicBusinessId: string | null): string | null {
    if (!publicBusinessId) {
        return null;
    }
    return publicBusinessId.split(REVIEW_PUBLIC_BUSINESS_ID_SEPARATOR)[1] || null;
}

export function createIncrementedPublicBusinessId(currentReviewPublicBusinessIdCount: number, organizationName: string): string {
    const options = {
        lower: true,
        strict: true,
        remove: /['|]/g,
        locale: 'en',
    };

    const organizationPrefix = slugify(organizationName, options);
    const newCount = currentReviewPublicBusinessIdCount + 1;

    const newCountAsHexa = newCount.toString(16).toUpperCase();
    const newPrettyCountAsHexa = String(newCountAsHexa).padStart(5, '0');
    return `${organizationPrefix}${REVIEW_PUBLIC_BUSINESS_ID_SEPARATOR}${newPrettyCountAsHexa}`;
}
