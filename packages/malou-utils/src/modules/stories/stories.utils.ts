import { DateTime } from 'luxon';

import { RecurrentStoryFrequency } from './stories.interface';

export function getNextOccurrence(startDate: Date, frequency: RecurrentStoryFrequency): Date {
    const next = new Date(startDate);

    switch (frequency) {
        case RecurrentStoryFrequency.DAILY:
            next.setDate(next.getDate() + 1);
            break;

        case RecurrentStoryFrequency.WEEKLY:
            next.setDate(next.getDate() + 7);
            break;

        case RecurrentStoryFrequency.WEEKLY_WEEKDAYS:
            // move to next weekday (monday to friday)
            do {
                next.setDate(next.getDate() + 1);
            } while (next.getDay() === 0 || next.getDay() === 6); // 0=sunday, 6=saturday
            break;

        case RecurrentStoryFrequency.WEEKLY_WEEKENDS:
            // move to next weekend (saturday or sunday)
            do {
                next.setDate(next.getDate() + 1);
            } while (next.getDay() !== 0 && next.getDay() !== 6);
            break;

        default:
            throw new Error(`Unknown frequency: ${frequency}`);
    }

    return next;
}

export function getOccurrencesBetween(startDate: Date, endDate: Date, frequency: RecurrentStoryFrequency, initialDate: Date): Date[] {
    const occurrences: Date[] = [];

    // Find the first occurrence on or after startDate that matches the pattern from initialDate
    let current = findFirstOccurrenceFromInitialDate(startDate, frequency, initialDate);

    // If the first occurrence is after endDate, return empty array
    if (current > endDate) {
        return occurrences;
    }

    // Add the first occurrence if it's within range
    if (current >= startDate && current <= endDate) {
        occurrences.push(new Date(current));
    }

    // Generate subsequent occurrences
    while (current < endDate) {
        current = getNextOccurrence(current, frequency);

        if (current <= endDate) {
            occurrences.push(new Date(current));
        }
    }

    return occurrences;
}

function findFirstOccurrenceFromInitialDate(startDate: Date, frequency: RecurrentStoryFrequency, initialDate: Date): Date {
    if (initialDate >= startDate) {
        return new Date(initialDate);
    }

    const start = DateTime.fromJSDate(startDate);
    const initial = DateTime.fromJSDate(initialDate);
    const diff = start.diff(initial, 'days');

    let date: DateTime;

    switch (frequency) {
        case RecurrentStoryFrequency.DAILY:
            return initial.plus({ days: diff.days }).toJSDate();
        case RecurrentStoryFrequency.WEEKLY:
            return initial.plus({ weeks: Math.ceil(diff.days / 7) }).toJSDate();
        case RecurrentStoryFrequency.WEEKLY_WEEKDAYS:
            date = initial.plus({ weeks: Math.ceil(diff.days / 7) });
            while (date.weekday === 6 || date.weekday === 7) {
                date = date.plus({ days: 1 });
            }
            return date.toJSDate();
        case RecurrentStoryFrequency.WEEKLY_WEEKENDS:
            date = initial.plus({ weeks: Math.ceil(diff.days / 7) });
            while (date.weekday !== 6 && date.weekday !== 7) {
                date = date.plus({ days: 1 });
            }
            return date.toJSDate();
        default:
            return new Date(initialDate);
    }
}
