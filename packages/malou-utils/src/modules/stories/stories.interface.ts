export enum StoriesListFilter {
    ALL = 'all',
    DRAFT = 'draft',
    ERROR = 'error',
    FEEDBACK = 'feedback',
    RECURRENT = 'recurrent',
}

export enum RecurrentStoryFrequency {
    NONE = 'none',
    DAILY = 'daily', // Every day
    WEEKLY = 'weekly', // Every week, on the same day of the week
    WEEKLY_WEEKDAYS = 'weekly_weekdays', // Every week, Monday to Friday
    WEEKLY_WEEKENDS = 'weekly_weekends', // Every week, Saturday and Sunday
}
