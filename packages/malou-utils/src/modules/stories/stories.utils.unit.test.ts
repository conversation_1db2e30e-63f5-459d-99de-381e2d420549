import { RecurrentStoryFrequency } from './stories.interface';
import { getNextOccurrence, getOccurrencesBetween } from './stories.utils';

describe('stories.utils', () => {
    describe('getOccurrencesBetween', () => {
        describe('WEEKLY frequency', () => {
            it('should return occurrences based on initialDate pattern - user example case', () => {
                // User's example:
                // Frequency: WEEKLY
                // StartDate: Monday, May 8th, 2023
                // EndDate: Sunday, May 14th, 2023
                // InitialDate: Wednesday, May 3rd, 2023
                // Expected: Wednesday, May 10th, 2023

                const startDate = new Date('2023-05-08'); // Monday
                const endDate = new Date('2023-05-14'); // Sunday
                const initialDate = new Date('2023-05-03'); // Wednesday
                const frequency = RecurrentStoryFrequency.WEEKLY;

                const occurrences = getOccurrencesBetween(startDate, endDate, frequency, initialDate);

                expect(occurrences).toHaveLength(1);
                expect(occurrences[0].toDateString()).toBe('Wed May 10 2023');
            });

            it('should return multiple weekly occurrences when range spans multiple weeks', () => {
                const startDate = new Date('2023-05-08'); // Monday
                const endDate = new Date('2023-05-21'); // Sunday (2 weeks later)
                const initialDate = new Date('2023-05-03'); // Wednesday
                const frequency = RecurrentStoryFrequency.WEEKLY;

                const occurrences = getOccurrencesBetween(startDate, endDate, frequency, initialDate);

                expect(occurrences).toHaveLength(2);
                expect(occurrences[0].toDateString()).toBe('Wed May 10 2023');
                expect(occurrences[1].toDateString()).toBe('Wed May 17 2023');
            });

            it('should return empty array when no occurrences fall within range', () => {
                const startDate = new Date('2023-05-08'); // Monday
                const endDate = new Date('2023-05-09'); // Tuesday
                const initialDate = new Date('2023-05-03'); // Wednesday
                const frequency = RecurrentStoryFrequency.WEEKLY;

                const occurrences = getOccurrencesBetween(startDate, endDate, frequency, initialDate);

                expect(occurrences).toHaveLength(0);
            });

            it('should include initialDate if it falls within the range', () => {
                const startDate = new Date('2023-05-01'); // Monday
                const endDate = new Date('2023-05-07'); // Sunday
                const initialDate = new Date('2023-05-03'); // Wednesday (within range)
                const frequency = RecurrentStoryFrequency.WEEKLY;

                const occurrences = getOccurrencesBetween(startDate, endDate, frequency, initialDate);

                expect(occurrences).toHaveLength(1);
                expect(occurrences[0].toDateString()).toBe('Wed May 03 2023');
            });
        });

        describe('DAILY frequency', () => {
            it('should return daily occurrences starting from initialDate pattern', () => {
                const startDate = new Date('2023-05-08'); // Monday
                const endDate = new Date('2023-05-10'); // Wednesday
                const initialDate = new Date('2023-05-05'); // Friday (before range)
                const frequency = RecurrentStoryFrequency.DAILY;

                const occurrences = getOccurrencesBetween(startDate, endDate, frequency, initialDate);

                expect(occurrences).toHaveLength(3);
                expect(occurrences[0].toDateString()).toBe('Mon May 08 2023');
                expect(occurrences[1].toDateString()).toBe('Tue May 09 2023');
                expect(occurrences[2].toDateString()).toBe('Wed May 10 2023');
            });
        });

        describe('WEEKLY_WEEKDAYS frequency', () => {
            it('should return only weekday occurrences', () => {
                const startDate = new Date('2023-05-08'); // Monday
                const endDate = new Date('2023-05-14'); // Sunday
                const initialDate = new Date('2023-05-01'); // Monday (weekday)
                const frequency = RecurrentStoryFrequency.WEEKLY_WEEKDAYS;

                const occurrences = getOccurrencesBetween(startDate, endDate, frequency, initialDate);

                // Should include Mon, Tue, Wed, Thu, Fri (5 weekdays)
                expect(occurrences).toHaveLength(5);
                expect(occurrences[0].toDateString()).toBe('Mon May 08 2023');
                expect(occurrences[1].toDateString()).toBe('Tue May 09 2023');
                expect(occurrences[2].toDateString()).toBe('Wed May 10 2023');
                expect(occurrences[3].toDateString()).toBe('Thu May 11 2023');
                expect(occurrences[4].toDateString()).toBe('Fri May 12 2023');
            });
        });

        describe('WEEKLY_WEEKENDS frequency', () => {
            it('should return only weekend occurrences', () => {
                const startDate = new Date('2023-05-08'); // Monday
                const endDate = new Date('2023-05-14'); // Sunday
                const initialDate = new Date('2023-05-06'); // Saturday (weekend)
                const frequency = RecurrentStoryFrequency.WEEKLY_WEEKENDS;

                const occurrences = getOccurrencesBetween(startDate, endDate, frequency, initialDate);

                // Should include Sat, Sun (2 weekend days)
                expect(occurrences).toHaveLength(2);
                expect(occurrences[0].toDateString()).toBe('Sat May 13 2023');
                expect(occurrences[1].toDateString()).toBe('Sun May 14 2023');
            });
        });
    });

    describe('getNextOccurrence', () => {
        it('should return next day for DAILY frequency', () => {
            const startDate = new Date('2023-05-08');
            const next = getNextOccurrence(startDate, RecurrentStoryFrequency.DAILY);
            expect(next.toDateString()).toBe('Tue May 09 2023');
        });

        it('should return same day next week for WEEKLY frequency', () => {
            const startDate = new Date('2023-05-08'); // Monday
            const next = getNextOccurrence(startDate, RecurrentStoryFrequency.WEEKLY);
            expect(next.toDateString()).toBe('Mon May 15 2023');
        });
    });
});
