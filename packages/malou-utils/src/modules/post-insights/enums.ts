export enum PostInsightStoryMetric {
    IMPRESSIONS = 'impressions',
    REACH = 'reach',
    TAPS_FORWARD = 'taps_forward',
    TAPS_BACK = 'taps_back',
    REPLIES = 'replies',
    TAPS_EXITS = 'taps_exits',
}

export enum PostInsightFbPostMetric {
    IMPRESSIONS = 'impressions', // 'post_impressions' on facebook api
    LIKES = 'likes',
    COMMENTS = 'comments',
    SHARES = 'shares',
}

export enum PostInsightIgPostMetric {
    IMPRESSIONS = 'impressions',
    REACH = 'reach',
    PLAYS = 'plays',
    LIKES = 'likes',
    COMMENTS = 'comments',
    SAVED = 'saved',
    SHARES = 'shares',
}

export enum PostInsightFbReelMetric {
    SHARES = 'shares',
    IMPRESSIONS = 'impressions',
    LIKES = 'likes',
    COMMENTS = 'comments',
    PLAYS = 'plays',
}

export enum PostInsightIgReelMetric {
    IMPRESSIONS = 'impressions',
    REACH = 'reach',
    PLAYS = 'plays',
    LIKES = 'likes',
    COMMENTS = 'comments',
    SAVED = 'saved',
    SHARES = 'shares',
}

export enum PostInsightEntityType {
    STORY = 'story',
    POST = 'post',
    REEL = 'reel',
}
