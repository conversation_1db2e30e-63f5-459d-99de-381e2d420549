import { z } from 'zod';

import { StoreLocatorLanguage } from '@malou-io/package-utils';

import { storeLocatorDraftStoreValidator, storeLocatorStoreValidator } from './get-store-page';
import { getStoreLocatorMapValidator } from './store-map';

export const getStoreLocatorPagesValidator = z.object({
    restaurantsPages: z.array(storeLocatorStoreValidator),
    mapPages: z.array(getStoreLocatorMapValidator),
    urls: z.record(
        z.nativeEnum(StoreLocatorLanguage),
        z.record(
            z.string(), // e.g., 'store.12345' or 'map'
            z.string() // e.g., '/restaurants/12345' or '/map'
        )
    ),
});

export type GetStoreLocatorPagesDto = z.infer<typeof getStoreLocatorPagesValidator>;

export const getStoreLocatorDraftPagesValidator = z.object({
    restaurantsPages: z.array(storeLocatorDraftStoreValidator),
});

export type GetStoreLocatorDraftPagesDto = z.infer<typeof getStoreLocatorDraftPagesValidator>;

// ----------------------------------------------------------------

export const getStoreLocatorCentralizationDraftValidator = z.object({
    centralizationPages: z.array(getStoreLocatorMapValidator),
});

export type GetStoreLocatorCentralizationDraftDto = z.infer<typeof getStoreLocatorCentralizationDraftValidator>;
