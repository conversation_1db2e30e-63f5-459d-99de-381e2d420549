import { z } from 'zod';

import { imageValidator } from '../shared';

const storeLocatorMapPopupValidator = z.object({
    noStoreImage: imageValidator,
});

const storeLocatorMapPinsValidator = z.object({
    activePin: imageValidator,
    inactivePin: imageValidator.optional(),
});

export const storeLocatorMapComponentsValidator = z.object({
    pins: storeLocatorMapPinsValidator,
    popup: storeLocatorMapPopupValidator.optional(),
});
