import { Malou<PERSON><PERSON>r<PERSON><PERSON>, MalouMetric, MediaType, PlatformKey, PostInsightEntityType, PostType } from '@malou-io/package-utils';

export interface PostInsightDto {
    id: string;
    platformKey: PlatformKey;
    socialId: string;
    entityType: PostInsightEntityType;
    platformSocialId: string;
    postSocialCreatedAt: string;
    followersCountAtPostTime: number | null;
    data: {
        impressions: number;
        likes: number;
        comments: number;
        shares: number;
        reach: number | null;
        plays: number | null;
        saved: number | null;
    };
    post: {
        id: string;
        text: string;
        postType: PostType;
        socialLink?: string;
        // only first attachment is needed for the post insight
        attachment: {
            socialId: string | null;
            thumbnailUrl: string | null;
            type: MediaType | undefined;
            urls: {
                original: string;
            };
        } | null;
    };
}

export interface PlatformPostInsightResponseDto {
    platformKey: PlatformKey;
    postInsights: PostInsightDto[];
    error?: {
        code: MalouErrorCode;
        message: string;
    };
}

export interface AggregatedTopPostInsightDto {
    restaurantName: string;
    restaurantAddress?: string;
    platformKey: PlatformKey;
    postSocialCreatedAt: Date;
    postId: string;
    postType: PostType;
    mediaUrl?: string;
    thumbnailUrl?: string;
    impressions: number;
    comments: number;
    likes: number;
    shares: number | null;
    saves: number | null;
    engagementRate: number;
}

export interface AggregatedSocialPostInsightDto {
    [restaurantId: string]: {
        [platformKey: string]: {
            [MalouMetric.IMPRESSIONS]: number | null;
            [MalouMetric.ENGAGEMENTS]: number | null;
            [MalouMetric.POSTS]: number | null;
            [MalouMetric.FOLLOWERS]: number | null;
        };
    };
}
