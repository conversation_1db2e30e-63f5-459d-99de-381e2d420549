import { z } from 'zod';

import { PostPublicationStatus } from '@malou-io/package-utils';

export const pollingStoriesStatusesBodyValidator = z.object({
    bindingIds: z.array(z.string()),
});
export type PollingStoriesStatusesBodyDto = z.infer<typeof pollingStoriesStatusesBodyValidator>;

export type PollingStoriesStatusResponseDto = {
    postId: string;
    published: PostPublicationStatus;
    isPublishing: boolean;
    attachmentsLength: number;
    bindingId?: string;
};
