import { ApplicationLanguage } from '@malou-io/package-utils';

import { AddressDto, PhoneDto } from '../restaurant';

export interface LightClientAddressDto {
    locality?: string | null;
    country?: string | null;
    postalCode?: string | null;
    formattedAddress?: string | null;
}

export interface LightClientDto {
    id: string;
    civility?: string;
    firstName?: string;
    lastName?: string;
    address?: LightClientAddressDto;
    phone?: PhoneDto;
    email?: string;
    language?: string | null;
}

export interface ClientWithPrivateReviewRatingsDto {
    _id: string;
    restaurantId: string;
    lastName?: string;
    firstName?: string;
    civility?: string;
    email?: string;
    phone?: PhoneDto;
    address?: Omit<AddressDto, 'regionCode'>;
    language?: ApplicationLanguage;
    source?: string;
    lastVisitedAt?: Date;
    lastContactedAt?: Date;
    contactCount?: number;
    visitCount?: number;
    accepts?: string[];
    reviewsLeft?: {
        platformKey: string;
        hasLeftReview: boolean;
    }[];
    createdAt?: Date;
    birthday?: Date;
    privateReviewsRating?: {
        rating: number;
        socialCreatedAt: Date;
    }[];
}
