import { Column, Container, Img, Row, Section, Text } from '@react-email/components';
import { DateTime } from 'luxon';
import type { LocalizedString } from 'typesafe-i18n';

import { ConcernedRestaurantProps } from '@malou-io/package-dto';
import { Locale, ReportType } from '@malou-io/package-utils';

import { TranslationFunctions } from ':i18n/i18n-types';
import { MALOU_LOGO, MAX_CONCERNED_RESTAURANTS } from ':shared/constants';
import { Translation } from ':shared/services';
import { MalouReportHeaderProps } from ':shared/types';

/**
 *
 * A styled header for Malou emails.
 *
 * @param {MalouReportHeaderProps} props
 * @returns {React.ReactNode}
 */
export const MalouReportHeader = (props: MalouReportHeaderProps) => {
    const { reportType, concernedRestaurants, startDate, endDate, locale, trackingUrl } = props;
    const translate = new Translation(locale).getTranslator();
    return (
        <Container className="!max-w-[600px] border-b border-solid border-[#F2F2FF] px-7">
            <Section>
                <Row>
                    <Column className="pt-8 align-top" align="left">
                        {/** Tracking */}
                        {trackingUrl && <Img src={trackingUrl} width={1} className="collapse" height={1} />}
                        {/** */}
                        <Img src={MALOU_LOGO} height="17" alt="Malou Logo" />
                    </Column>
                    <Column className="py-5 text-right" align="right">
                        <Row>
                            <Column align="right">
                                <Text
                                    className={`${getNotificationBackgroundColor(
                                        reportType
                                    )} rounded  px-3 py-2 text-center text-[10px] font-semibold m-0 w-fit leading-[16px] no-underline`}>
                                    {getEmailNotificationTitle(translate, reportType)}
                                </Text>
                            </Column>
                        </Row>

                        <Text className="m-0 mt-2 text-[12px] italic leading-[20px] text-malou-color-text-2">
                            {getNotificationDisplayDate(reportType, startDate, endDate, locale)}
                        </Text>
                        {concernedRestaurants && concernedRestaurants.length > 0 && (
                            <Text className="m-0 mt-2 text-end text-[10px] italic leading-[20px] text-malou-color-text-2">
                                {getConcernedRestaurantsDisplayName(concernedRestaurants, translate)}
                            </Text>
                        )}
                    </Column>
                </Row>
            </Section>
        </Container>
    );
};

const getNotificationBackgroundColor = (reportType: ReportType = ReportType.DAILY_REVIEWS): string => {
    switch (reportType) {
        case ReportType.MONTHLY_PERFORMANCE:
            return 'bg-malou-color-chart-purple--accent/[.15] text-malou-color-chart-purple--accent';
        case ReportType.WEEKLY_PERFORMANCE:
        case ReportType.WEEKLY_REVIEWS:
            return 'bg-malou-color-primary/[.15] text-malou-color-primary';
        case ReportType.DAILY_REVIEWS:
            return 'bg-malou-color-state-warn/[.15] text-malou-color-state-warn';
        default:
            return 'bg-malou-color-state-warn/[.15] text-malou-color-state-warn';
    }
};

const getEmailNotificationTitle = (translate: TranslationFunctions, reportType: ReportType = ReportType.DAILY_REVIEWS): LocalizedString => {
    switch (reportType) {
        case ReportType.MONTHLY_PERFORMANCE:
            return translate.reports.common.monthly_performance_report.normal();
        case ReportType.WEEKLY_PERFORMANCE:
            return translate.reports.common.weekly_performance_report.normal();
        case ReportType.WEEKLY_REVIEWS:
            return translate.reports.common.weekly_reviews_report();
        case ReportType.DAILY_REVIEWS:
            return translate.reports.common.daily_reviews_report();
        default:
            return translate.reports.common.notification();
    }
};

const getNotificationDisplayDate = (reportType: ReportType, startDate: Date, endDate: Date, locale: Locale): string => {
    const dateFormat = getDateFormatByLocale(locale);
    switch (reportType) {
        case ReportType.MONTHLY_PERFORMANCE:
        case ReportType.WEEKLY_PERFORMANCE:
        case ReportType.WEEKLY_REVIEWS:
            return `${DateTime.fromJSDate(startDate).toFormat(dateFormat, {
                locale,
            })} - ${DateTime.fromJSDate(endDate).toFormat(dateFormat, {
                locale,
            })}`;
        case ReportType.DAILY_REVIEWS:
            return DateTime.fromJSDate(startDate).toFormat(dateFormat, {
                locale,
            });
        default:
            return DateTime.now().toFormat(dateFormat, {
                locale,
            });
    }
};

const getDateFormatByLocale = (locale: Locale): string => {
    if (locale === Locale.EN) {
        return 'MMMM dd, yyyy';
    }
    return 'dd MMMM yyyy';
};

const getConcernedRestaurantsDisplayName = (
    concernedRestaurants: ConcernedRestaurantProps[],
    translate: TranslationFunctions
): React.ReactNode => {
    if (concernedRestaurants.length === 1) {
        return (
            <>
                <span className="m-0 mt-2 text-end text-[10px] font-bold italic text-malou-color-text-2">
                    {concernedRestaurants[0].name}
                </span>
                <br />
                <span className="m-0 mt-2 text-end text-[10px] italic text-malou-color-text-2">{concernedRestaurants[0].address}</span>
            </>
        );
    }
    return concernedRestaurants.length <= MAX_CONCERNED_RESTAURANTS
        ? concernedRestaurants.map((e) => e.name).join(', ')
        : translate.reports.common.concerned_restaurants_number.multiple({
              data: concernedRestaurants.length,
          });
};

MalouReportHeader.defaultProps = {
    reportType: ReportType.DAILY_REVIEWS,
    trackingUrl: '',
};
