import { ReactElement } from 'react';

import { BaseEmailProps, ConcernedRestaurantProps } from '@malou-io/package-dto';
import { EmailPadding, Locale, ReportType } from '@malou-io/package-utils';

import { EmailMargin } from './report.enum';

export interface MalouLayoutProps {
    children: React.ReactNode;
    context: BaseEmailProps;
    customHeader?: ReactElement;
    showHeader?: boolean;
    showFooter?: boolean;
    paddingX?: EmailPadding;
    emailMarginY?: EmailMargin;
    footerMarginTop?: EmailMargin;
}

export interface MalouReportHeaderProps {
    reportType: ReportType;
    concernedRestaurants?: ConcernedRestaurantProps[];
    trackingUrl: string;
    locale: Locale;
    startDate: Date;
    endDate: Date;
}

export interface MalouBasicHeaderProps {
    trackingUrl: string;
    locale: Locale;
    notificationTitle?: string;
    dateTimeFormat?: string;
    xcss?: string;
}

export interface MalouFooterProps {
    footerMarginTop?: EmailMargin;
    unsubscribeLink?: string;
    locale: Locale;
}

export enum TextSize {
    xs = 'xsmall',
    sm = 'small',
    md = 'medium',
    lg = 'large',
    xl = 'xlarge',
}

export interface MalouDownloadReviewsHeaderProps {
    concernedRestaurants?: ConcernedRestaurantProps[];
    locale: Locale;
    startDate: Date;
    endDate: Date;
    timeZone: string;
}
