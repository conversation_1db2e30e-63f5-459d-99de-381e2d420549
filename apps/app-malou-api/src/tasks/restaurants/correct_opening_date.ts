import 'reflect-metadata';

import ':env';

import { DateTime } from 'luxon';
import { AnyBulkWriteOperation } from 'mongoose';
import { container, singleton } from 'tsyringe';

import { PublishOnGmbPlatformUseCase } from ':modules/platforms/use-cases/publish-on-connected-platforms/platforms/publish-on-gmb-platform.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

@singleton()
class CorrectOpeningDateTask {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _publishOnGmbPlatformUseCase: PublishOnGmbPlatformUseCase
    ) {}

    async execute() {
        const restaurants = await this._restaurantsRepository.aggregate([
            {
                $project: {
                    openingDate: '$openingDate',
                    openingHour: {
                        $hour: '$openingDate',
                    },
                },
            },
            {
                $match: {
                    openingHour: 22,
                },
            },
        ]);
        const bulkWriteOperations: AnyBulkWriteOperation[] = restaurants.map((restaurant) => {
            const dateWithCorrectTimeZone = DateTime.fromJSDate(restaurant.openingDate).toUTC().plus({ hours: 2 }).toJSDate();
            return {
                updateOne: {
                    filter: { _id: restaurant._id },
                    update: { $set: { openingDate: dateWithCorrectTimeZone } },
                },
            };
        });
        console.log(`Total restaurants to update: ${bulkWriteOperations.length}`);
        if (bulkWriteOperations.length > 0) {
            await this._restaurantsRepository.bulkOperations({ operations: bulkWriteOperations });
        }
        const restaurantsWithFutureOpeningDate = restaurants.filter((restaurant) => restaurant.openingDate > new Date());
        for (const restaurant of restaurantsWithFutureOpeningDate) {
            const res = await this._publishOnGmbPlatformUseCase.execute({
                restaurant,
                keysToUpdate: ['openingDate'],
                options: { validateOnly: false },
            });
            console.log(`Publish on GMB for restaurant ${restaurant._id} : ${res.success ? 'Success' : 'Failed'}`, res);
        }
    }
}

const task = container.resolve(CorrectOpeningDateTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
