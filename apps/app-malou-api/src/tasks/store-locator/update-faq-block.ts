import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { GenerateStoreLocatorContentType, StoreLocatorLanguage } from '@malou-io/package-utils';

import { GenerateStorePageContentService } from ':modules/store-locator/services/generate-store-page-content/generate-store-page-content.service';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
class UpdateFaqBlockTask {
    constructor(
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _storeLocatorOrganizationConfigurationRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _generateStorePageContentService: GenerateStorePageContentService
    ) {}

    async execute(): Promise<void> {
        const storeLocatorOrganizationsConfigurations =
            await this._storeLocatorOrganizationConfigurationRepository.getAllOrganizationConfigurations();

        console.log('Found', storeLocatorOrganizationsConfigurations.length, 'store locator organizations configurations');

        for (const config of storeLocatorOrganizationsConfigurations) {
            console.log('Processing organization configuration for organization:', config.organizationId);

            // Get Organization Pages
            const organizationStoreLocatorPages = await this._storeLocatorRestaurantPageRepository.find({
                filter: {
                    organizationId: config.organizationId,
                },
                options: { lean: true },
            });

            await Promise.all(
                organizationStoreLocatorPages.map(async (storeLocatorPage) => {
                    console.log('Processing store locator page:', storeLocatorPage._id, 'for organization:', config.organizationId);

                    const faqsBlock = await this._generateStorePageContentService.generateSpecificPageContent({
                        type: GenerateStoreLocatorContentType.FAQ_BLOCK_GENERATION,
                        lang: storeLocatorPage.lang,
                        storeLocatorRestaurantPage: storeLocatorPage,
                        storeLocatorOrganizationConfig: config,
                    });
                    const faqBlockContent = {
                        title: this._getTitleByLang(storeLocatorPage.lang),
                        items: faqsBlock.faqs,
                    };

                    await this._storeLocatorRestaurantPageRepository.updateOne({
                        filter: { _id: storeLocatorPage._id },
                        update: { $set: { 'blocks.faq': faqBlockContent } },
                    });
                })
            );

            console.log('Updating FAQ blocks style config for organization:', config.organizationId);

            await this._storeLocatorOrganizationConfigurationRepository.updateOne({
                filter: {
                    organizationId: config.organizationId,
                },
                update: {
                    $set: {
                        'styles.pages.store.faq-wrapper': ['bg-primary'],
                        'styles.pages.store.faq-title': ['text-tertiary'],
                        'styles.pages.store.faq-item': ['bg-primary'],
                        'styles.pages.store.faq-item-question': ['text-tertiary'],
                        'styles.pages.store.faq-item-answer': ['text-tertiary'],
                        'styles.pages.store.faq-icon-wrapper': ['bg-white'],
                        'styles.pages.store.faq-icon': ['fill-primary'],
                    },
                },
            });
        }
    }

    private _getTitleByLang(lang: StoreLocatorLanguage): string {
        switch (lang) {
            case StoreLocatorLanguage.FR:
                return 'Foire Aux Questions';
            case StoreLocatorLanguage.IT:
                return 'Domande Frequenti';
            case StoreLocatorLanguage.ES:
                return 'Preguntas Frecuentes';
            case StoreLocatorLanguage.EN:
            default:
                return 'Frequently Asked Questions';
        }
    }
}

const task = container.resolve(UpdateFaqBlockTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
