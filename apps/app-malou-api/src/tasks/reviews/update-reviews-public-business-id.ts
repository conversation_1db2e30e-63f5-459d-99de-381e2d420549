import 'reflect-metadata';

import ':env';

import { groupBy, uniq } from 'lodash';
import pLimit from 'p-limit';
import { container, singleton } from 'tsyringe';

import { OrganizationModel, PrivateReviewModel, ReviewModel, toDbId, toDbIds } from '@malou-io/package-models';
import { createIncrementedPublicBusinessId } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class UpdatePublicBusinessIdsTask {
    private readonly ORG_BATCH_SIZE = 20;
    private readonly REVIEW_BATCH_SIZE = 500;

    private readonly CLEAN_DB = false; // Set to true to clean the DB before running the task
    // !!! Warning: this will delete all publicBusinessId and reviewPublicBusinessIdCount in the DB

    private readonly ORGANIZATION_IDS: string[] = []; // !!! Fill the organization ids that need to be processed

    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _organizationsRepository: OrganizationsRepository
    ) {}

    async execute() {
        if (this.CLEAN_DB) {
            logger.info('Cleaning DB before running the task…');
            await this._cleanDb();
            logger.info('DB cleaned');
        }
        console.time('Task finished in');
        logger.info('Starting publicBusinessId update for reviews…');
        await this._processByReviewType(ReviewModel, 'reviews');
        logger.info('Starting publicBusinessId update for private reviews…');
        await this._processByReviewType(PrivateReviewModel, 'private reviews');
        logger.info('All done!');
        console.timeEnd('Task finished in');
    }

    private async _processByReviewType(model: typeof ReviewModel | typeof PrivateReviewModel, label: string) {
        const reviewOrPrivateReviewModel = model as any;
        const restaurantIds = await reviewOrPrivateReviewModel.distinct('restaurantId');

        const restaurants = await this._restaurantsRepository.find({
            filter: { _id: { $in: toDbIds(restaurantIds) }, organizationId: { $in: toDbIds(this.ORGANIZATION_IDS) } },
            projection: { organizationId: 1, _id: 1 },
            options: { lean: true },
        });

        const organizationIds = uniq(restaurants.map((r) => r.organizationId.toString()));
        logger.info(`Found ${organizationIds.length} organizations`);

        const grouped = groupBy(restaurants, (r) => r.organizationId.toString());

        const limit = pLimit(this.ORG_BATCH_SIZE);
        await Promise.all(
            Object.entries(grouped).map(([orgId, rests]) =>
                limit(async () => {
                    await this._processModel({
                        organizationId: orgId,
                        restaurantIds: rests.map((r) => r._id.toString()),
                        model,
                        label: `${label} for org ${orgId}`,
                    });
                })
            )
        );
    }

    private async _cleanDb() {
        await OrganizationModel.updateMany(
            {
                reviewPublicBusinessIdCount: { $exists: true },
            },
            {
                $set: { reviewPublicBusinessIdCount: 0 },
            }
        );
        await ReviewModel.updateMany(
            {
                publicBusinessId: { $exists: true },
            },
            {
                $unset: { publicBusinessId: '' },
            }
        );
        await PrivateReviewModel.updateMany(
            {
                publicBusinessId: { $exists: true },
            },
            {
                $unset: { publicBusinessId: '' },
            }
        );
    }

    private async _processModel({
        organizationId,
        restaurantIds,
        model,
        label,
    }: {
        organizationId: string;
        restaurantIds: string[];
        model: typeof ReviewModel | typeof PrivateReviewModel;
        label: string;
    }) {
        let total = 0;
        let docs: any[] = [];
        const reviewOrPrivateReviewModel = model as any;

        const organization = await this._organizationsRepository.findOne({
            filter: { _id: toDbId(organizationId) },
            projection: { reviewPublicBusinessIdCount: 1, name: 1 },
            options: { lean: true },
        });
        if (!organization) {
            logger.warn(`Organization ${organizationId} not found, skipping ${label}`);
            return;
        }

        const cursor = reviewOrPrivateReviewModel
            .find({ restaurantId: { $in: toDbIds(restaurantIds) } }, { _id: 1, restaurantId: 1 })
            .lean()
            .sort({ socialCreatedAt: -1 })
            .cursor({ batchSize: this.REVIEW_BATCH_SIZE });

        for await (const doc of cursor) {
            docs.push(doc);

            if (docs.length >= this.REVIEW_BATCH_SIZE) {
                await this._processReviewBatch({
                    organizationId,
                    docs,
                    processedCount: total,
                    reviewPublicBusinessIdCount: organization.reviewPublicBusinessIdCount,
                    organizationName: organization.name,
                    model,
                    label,
                });
                total += docs.length;
                docs = [];
            }
        }

        if (docs.length) {
            await this._processReviewBatch({
                organizationId,
                docs,
                processedCount: total,
                reviewPublicBusinessIdCount: organization.reviewPublicBusinessIdCount,
                organizationName: organization.name,
                model,
                label,
            });
        }
    }

    private async _processReviewBatch({
        organizationId,
        docs,
        processedCount = 0,
        reviewPublicBusinessIdCount,
        organizationName,
        model,
        label,
    }: {
        organizationId: string;
        docs: any[];
        processedCount: number;
        reviewPublicBusinessIdCount: number;
        organizationName: string;
        model: typeof ReviewModel | typeof PrivateReviewModel;
        label: string;
    }) {
        const ops = docs.map((d, idx) => {
            const globalIndex = processedCount + idx + 1;
            const publicBusinessId = createIncrementedPublicBusinessId(reviewPublicBusinessIdCount + globalIndex, organizationName);

            return {
                updateOne: {
                    filter: { _id: d._id, publicBusinessId: { $exists: false } },
                    update: { $set: { publicBusinessId } },
                },
            };
        });

        await (model as any).bulkWrite(ops, { ordered: false });
        logger.info(`→ ${label}: processed ${processedCount + docs.length}`);

        await this._organizationsRepository.findOneAndUpdate({
            filter: { _id: toDbId(organizationId) },
            update: { $inc: { reviewPublicBusinessIdCount: docs.length } },
        });

        processedCount += docs.length;
    }
}

const task = container.resolve(UpdatePublicBusinessIdsTask);
task.execute()
    .then(() => process.exit(0))
    .catch((err) => {
        console.error(err);
        process.exit(1);
    });
