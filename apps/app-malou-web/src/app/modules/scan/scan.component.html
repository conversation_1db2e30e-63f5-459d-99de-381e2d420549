<div class="h-screen w-screen overflow-y-auto">
    @if (!isError) {
        @if (data$ | async; as data) {
            @if (!isRedirectionInProgress) {
                <ng-container [ngTemplateOutlet]="pageTemplate" [ngTemplateOutletContext]="{ restaurant: data.nfc.restaurant }">
                </ng-container>
            } @else {
                <div class="flex min-h-full flex-col items-center justify-center gap-y-8 px-8">
                    {{ 'scan.redirection_in_progress' | translate }}
                </div>
            }
        } @else {
            <div class="flex min-h-full flex-col items-center justify-center gap-y-8 px-8">
                <app-malou-spinner></app-malou-spinner>
            </div>
        }
    } @else {
        @if (boosterPackDeactivated) {
            <ng-container [ngTemplateOutlet]="boosterPackDeactivatedTemplate"></ng-container>
        } @else {
            <div class="flex min-h-full flex-col items-center justify-center gap-y-8 px-8">
                <div><img class="w-24" [src]="Illustration.Cook | illustrationPathResolver" /></div>
                <div>{{ 'scan.error' | translate }}</div>
            </div>
        }
    }
</div>

<ng-template let-restaurant="restaurant" #pageTemplate>
    @let cover = restaurant.coverPopulated?.urls?.original;
    @let logo = restaurant.logoPopulated?.urls?.original;
    <div class="flex h-full flex-col">
        @if (cover || logo) {
            <div
                class="relative flex flex-col items-center transition-all duration-300 ease-linear"
                [ngClass]="{
                    'h-[25vh]': cover,
                    'h-[15vh]': !cover,
                }">
                @if (cover) {
                    <img class="h-full w-screen object-cover" alt="cover" [src]="cover" />
                }
                @if (logo) {
                    <img
                        class="malou-box-shadow h-24 w-24 rounded-full bg-white"
                        alt="logo"
                        [ngClass]="{
                            'mt-10': !cover,
                            'absolute -bottom-12': cover,
                        }"
                        [src]="logo" />
                }
            </div>
        }
        <div class="flex grow flex-col items-center justify-center gap-y-8 px-8">
            <div class="malou-text-13--regular malou-color-text-2 mb-4 text-center">
                {{ 'scan.thank_you' | translate }}
            </div>
            <div>
                <p class="malou-text-25--bold malou-color-text-1 text-center">{{ 'scan.title' | translate }}</p>
                <p class="malou-text-25--bold malou-color-text-1 text-center">
                    {{ restaurant?.totemDisplayName?.title || restaurant?.name }}
                </p>
            </div>

            <div class="bg-malou-color-background-gray--light mt-12 w-72 rounded-[10px] p-6">
                <app-rate-with-stars class="h-10" (starClicked)="onStarClicked($event)"></app-rate-with-stars>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #boosterPackDeactivatedTemplate>
    <div class="flex flex-col items-center">
        <div class="relative ml-5" [ngClass]="{ '!mb-[20%] mt-[30%]': isPhoneScreen(), 'my-[5%]': !isPhoneScreen() }">
            <div class="absolute bottom-0 right-[20px] h-[140px] w-[140px] rounded-full bg-malou-color-primary"></div>
            <img class="relative w-[140px]" alt="" [src]="'pleading-face' | imagePathResolver: { folder: 'wheels-of-fortune' }" />
        </div>

        <div class="malou-text-24--semibold malou-color-text-1 text-center">
            {{ 'get_my_gift.image_with_background_templates.booster_pack_deactivated.title' | translate }}
        </div>
        <div
            class="malou-text-15--semibold malou-color-text-2 text-center leading-7"
            [ngClass]="{ 'mt-[25%]': isPhoneScreen(), 'mt-[5%]': !isPhoneScreen() }">
            {{ 'get_my_gift.image_with_background_templates.booster_pack_deactivated.description' | translate }}
        </div>
    </div>
</ng-template>
