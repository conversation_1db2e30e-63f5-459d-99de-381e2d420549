import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';

import {
    DuplicateStoriesResponseDto,
    GetPublishedStoriesCountDto,
    GetStoriesCountsDto,
    PollingStoriesStatusResponseDto,
    StoryItemDto,
    StoryToDuplicateDto,
} from '@malou-io/package-dto';
import {
    ApiResultV2,
    DeviceType,
    getFeatureFlaggedPlatforms,
    getPlatformKeysWithStories,
    PlatformKey,
    PostPublicationStatus,
    StoriesListFilter,
} from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { PlatformsService } from ':core/services/platforms.service';
import { environment } from ':environments/environment';
import { objectToQueryParams } from ':shared/helpers/query-params';

@Injectable({
    providedIn: 'root',
})
export class StoriesService {
    private readonly _http = inject(HttpClient);
    private readonly _API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/stories`;
    private readonly _experimentationService = inject(ExperimentationService);
    private readonly _platformsService = inject(PlatformsService);

    getStories$(
        selectedFilter: StoriesListFilter,
        cursor: string | null,
        restaurantId: string,
        limit: number
    ): Observable<{ storiesItems: StoryItemDto[]; nextCursor: string | null }> {
        const params = objectToQueryParams({ filter: selectedFilter, cursor, limit });

        return this._http
            .get<ApiResultV2<{ storiesItems: StoryItemDto[]; nextCursor: null | string }>>(
                `${this._API_BASE_URL}/restaurants/${restaurantId}/stories`,
                {
                    params,
                }
            )
            .pipe(map((res) => res.data));
    }

    pollingStoriesStatus$(bindingIds: string[]): Observable<ApiResultV2<PollingStoriesStatusResponseDto[]>> {
        return this._http.post<ApiResultV2<PollingStoriesStatusResponseDto[]>>(`${this._API_BASE_URL}/poll-status`, { bindingIds });
    }

    deleteStories$(storyIds: string[]): Observable<{ storyId: string; success: boolean }[]> {
        return this._http
            .post<ApiResultV2<{ storyId: string; success: boolean }[]>>(`${this._API_BASE_URL}/delete`, { storyIds })
            .pipe(map((res) => res.data));
    }

    getStoriesByIds$(storyIds: string[]): Observable<ApiResultV2<StoryItemDto[]>> {
        const params = objectToQueryParams({ storyIds });
        return this._http.get<ApiResultV2<StoryItemDto[]>>(`${this._API_BASE_URL}`, { params });
    }

    getStoriesCountByFilterOptions$(_restaurantId: string): Observable<{ filterOption: StoriesListFilter; count: number | null }[]> {
        return this._http.get<ApiResultV2<GetStoriesCountsDto>>(`${this._API_BASE_URL}/restaurants/${_restaurantId}/stories-counts`).pipe(
            map((res) => [
                {
                    filterOption: StoriesListFilter.ALL,
                    count: res.data.total,
                },
                { filterOption: StoriesListFilter.DRAFT, count: res.data.draft },
                { filterOption: StoriesListFilter.ERROR, count: res.data.error },
                { filterOption: StoriesListFilter.FEEDBACK, count: res.data.feedbacks },
                { filterOption: StoriesListFilter.RECURRENT, count: res.data.recurrent },
            ])
        );
    }

    getPublishedStoriesCount$({
        restaurantId,
        startDate,
        endDate,
        platformKeys,
    }: {
        restaurantId: string;
        startDate: Date;
        endDate: Date;
        platformKeys: PlatformKey[];
    }): Observable<number> {
        const params = objectToQueryParams({
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            platformKeys,
        });

        return this._http
            .get<
                ApiResultV2<GetPublishedStoriesCountDto>
            >(`${this._API_BASE_URL}/restaurants/${restaurantId}/published-stories-count`, { params })
            .pipe(map((res) => res.data.count));
    }

    duplicateStories$({
        fromRestaurantId,
        restaurantIds,
        postRefs,
        customFields,
    }: {
        fromRestaurantId: string;
        restaurantIds: string[];
        postRefs: ({ id: string } | { bindingId: string })[];
        customFields?: {
            restaurantId: string;
            plannedPublicationDate?: Date;
            platformKeys?: PlatformKey[];
            published?: PostPublicationStatus;
        }[];
    }): Observable<ApiResultV2<DuplicateStoriesResponseDto>> {
        return this._http.post<ApiResultV2<DuplicateStoriesResponseDto>>(
            `${this._API_BASE_URL}/restaurants/${fromRestaurantId}/duplicate`,
            {
                restaurantIds,
                postRefsToDuplicate: postRefs,
                customFields,
                createdFromDeviceType: DeviceType.DESKTOP,
            }
        );
    }

    getStoriesToDuplicate$(refs: ({ id: string } | { bindingId: string })[]): Observable<ApiResultV2<StoryToDuplicateDto[]>> {
        const params = objectToQueryParams({
            storyIds: refs.filter((r) => 'id' in r).map((r) => r.id),
            storyBindingIds: refs.filter((r) => 'bindingId' in r).map((r) => r.bindingId),
        });
        return this._http.get<ApiResultV2<StoryToDuplicateDto[]>>(`${this._API_BASE_URL}/to-duplicate`, { params });
    }

    getConnectedStoriesPlatformsForRestaurant$(restaurantId: string): Observable<PlatformKey[]> {
        const platformKeysToHide = this._getFeatureFlaggedPlatformKeysToHide();
        const potentialPlatformKeys = getPlatformKeysWithStories().filter((platformKey) => !platformKeysToHide.includes(platformKey));

        return this._platformsService.getPlatformsForRestaurant(restaurantId).pipe(
            map((res) => {
                const restaurantPlatformKeys = res.data.map((platform) => platform.key);
                return potentialPlatformKeys.filter((platformKey) => restaurantPlatformKeys.includes(platformKey));
            })
        );
    }

    private _getFeatureFlaggedPlatformKeysToHide(): PlatformKey[] {
        const featureFlaggedPlatforms = getFeatureFlaggedPlatforms();
        const featureFlaggedPlatformKeysToHide = featureFlaggedPlatforms
            .filter((p) => !this._experimentationService.isFeatureEnabled(p.featureFlagKey!))
            .map((p) => p.key);
        return featureFlaggedPlatformKeysToHide;
    }
}
