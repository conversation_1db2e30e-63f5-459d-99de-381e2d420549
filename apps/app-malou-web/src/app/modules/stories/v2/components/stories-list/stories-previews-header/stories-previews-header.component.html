<div
    class="flex h-[67px] items-center justify-between border-b border-l border-malou-color-border-primary bg-malou-color-background-white px-6 py-2"
    [ngClass]="{ 'cursor-pointer': hasMultiplePlatforms() }"
    [matMenuTriggerFor]="menu"
    #triggerDiv>
    <div class="flex items-center gap-x-3">
        @if (selectedPreviewPlatform(); as selectedPreviewPlatform) {
            <app-platform-logo imgClasses="h-8 w-8" [logo]="selectedPreviewPlatform.platformKey"></app-platform-logo>
            <span class="malou-text-12--bold text-malou-color-text-1">
                @if (selectedPreviewPlatform.platformKey === PlatformKey.INSTAGRAM) {
                    &#64;{{ selectedPreviewPlatform.username }}
                } @else {
                    {{ selectedPreviewPlatform.username }}
                }
            </span>
        } @else {
            <app-platform-logo imgClasses="h-8 w-8" [logo]="PlatformKey.INSTAGRAM"></app-platform-logo>
            <span class="malou-text-12--bold text-malou-color-text-1"> &#64;{{ DEFAULT_USERNAME }}</span>
        }
    </div>

    @if (hasMultiplePlatforms()) {
        <mat-icon class="h-4 w-4" color="primary" [svgIcon]="SvgIcon.CHEVRON_DOWN"></mat-icon>
    }
</div>

<mat-menu class="malou-mat-menu malou-box-shadow !rounded-b-[10px] !rounded-t-none" [xPosition]="'after'" #menu="matMenu">
    @for (option of previewPlatformOptions(); track option) {
        <button mat-menu-item [style.width.px]="triggerDiv.offsetWidth" (click)="onHeaderClicked(option)">
            <div class="flex gap-x-3">
                <app-platform-logo imgClasses="h-8 w-8" [logo]="option.platformKey"></app-platform-logo>
                <span class="malou-text-12--bold text-malou-color-text-1">
                    @if (option.platformKey === PlatformKey.INSTAGRAM) {
                        &#64;{{ option.username }}
                    } @else {
                        {{ option.username }}
                    }
                </span>
            </div>
        </button>
    }
</mat-menu>
