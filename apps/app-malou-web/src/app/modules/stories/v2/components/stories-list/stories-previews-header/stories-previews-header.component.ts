import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, model, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { filter, forkJoin, switchMap } from 'rxjs';

import { isNotNil, PlatformKey } from '@malou-io/package-utils';

import { PlatformsService } from ':core/services/platforms.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { PlatformOption } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/previews.component';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-stories-previews-header',
    templateUrl: './stories-previews-header.component.html',
    styleUrls: ['./stories-previews-header.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [NgClass, MatIconModule, MatMenuModule, PlatformLogoComponent],
})
export class StoriesPreviewsHeaderComponent {
    readonly selectedPreviewPlatform = model.required<PlatformOption | null>();
    readonly previewPlatformOptions = input.required<PlatformOption[]>();

    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _platformsService = inject(PlatformsService);

    readonly hasMultiplePlatforms = computed(() => this.previewPlatformOptions().length > 1);

    readonly PlatformKey = PlatformKey;
    readonly SvgIcon = SvgIcon;

    readonly DEFAULT_USERNAME = '--';

    readonly igUsername = signal<string>('');
    readonly fbUsername = signal<string>('');

    constructor() {
        this._restaurantsService.restaurantSelected$
            .pipe(
                filter(isNotNil),
                switchMap((restaurant) =>
                    forkJoin([
                        this._platformsService.getPlatformSocialLink(restaurant._id, PlatformKey.INSTAGRAM),
                        this._platformsService.getPlatformSocialLink(restaurant._id, PlatformKey.FACEBOOK),
                    ])
                ),
                takeUntilDestroyed()
            )
            .subscribe(([instagramRes, facebookRes]) => {
                this.igUsername.set(instagramRes.data?.socialLink?.match(/^https:\/\/www.instagram.com\/(.*)/)?.[1] ?? '');
                this.fbUsername.set(facebookRes.data?.name ?? '');
            });
    }

    onHeaderClicked(platformOption: PlatformOption): void {
        this.selectedPreviewPlatform.set(platformOption);
    }
}
