import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/models/store-locator-organization-config';
import { StoreLocatorOrganizationRestaurant } from ':modules/store-locator/models/store-locator-organization-restaurant';

// Input
export interface EditStoreLocatorCentralizationModalInputData {
    organizationConfiguration: StoreLocatorOrganizationConfiguration;
    organizationRestaurants: StoreLocatorOrganizationRestaurant[];
}

export enum StoreLocatorCentralizationBlockType {
    MAP = 'map',
}
