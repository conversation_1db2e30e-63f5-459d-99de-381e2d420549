import { computed, inject, Injectable, signal, WritableSignal } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { isNil } from 'lodash';
import { forkJoin, map, Observable, of } from 'rxjs';

import { ToastService } from ':core/services/toast.service';
import { StoreLocatorCentralizationBlockType } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.interface';
import { StoreLocatorStoreCentralizationState } from ':modules/store-locator/edit-store-locator-centralization/models/store-locator-store-centralization-state';
import { StoreLocatorOrganizationRestaurant } from ':modules/store-locator/models/store-locator-organization-restaurant';
import { StoreLocatorActivityTracker } from ':modules/store-locator/shared/edit-store-locator/models/store-locator-activity-tracker';
import { StoreLocatorOrganizationStylesConfiguration } from ':modules/store-locator/shared/edit-store-locator/models/store-locator-organization-styles-configuration';
import { StoreLocatorContext } from ':modules/store-locator/store-locator.context';
import { StoreLocatorService } from ':modules/store-locator/store-locator.service';

@Injectable({
    providedIn: 'root',
})
export class EditStoreLocatorCentralizationContext {
    private readonly _storeLocatorService = inject(StoreLocatorService);
    private readonly _storeLocatorContext = inject(StoreLocatorContext);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);

    readonly isSavingChanges: WritableSignal<boolean> = signal(false);
    readonly isPublishing: WritableSignal<boolean> = signal(false);

    readonly dialogRef: WritableSignal<any> = signal(null);

    readonly organizationData: WritableSignal<{ id: string; name: string }> = signal({ id: '', name: '' });
    readonly organizationRestaurants: WritableSignal<StoreLocatorOrganizationRestaurant[]> = signal([]);
    readonly organizationStyleConfiguration: WritableSignal<StoreLocatorOrganizationStylesConfiguration | null> = signal(null);

    readonly storeCentralizationPageState: WritableSignal<StoreLocatorStoreCentralizationState | null> = signal(null);
    readonly editStoreLocatorActivityTracker: WritableSignal<StoreLocatorActivityTracker | null> = signal(null);

    readonly isBlockInError: WritableSignal<{
        blockType: StoreLocatorCentralizationBlockType;
        isError: boolean;
    }> = signal({
        blockType: StoreLocatorCentralizationBlockType.MAP,
        isError: false,
    });

    readonly shouldDisableModal = computed(() => this.isSavingChanges() || this.isPublishing());

    getCentralizationPages(organizationId: string): Observable<StoreLocatorStoreCentralizationState[] | null> {
        return this._storeLocatorService
            .getCentralizationPages(organizationId)
            .pipe(map((res) => res.data.centralizationPages.map((page) => new StoreLocatorStoreCentralizationState(page))));
    }

    saveAsDraft(): void {
        this.isSavingChanges.set(true);
        const organizationId = this.organizationData().id;

        if (!organizationId) {
            console.error('No organization ID found for the current editing restaurant.');
            this.isSavingChanges.set(false);
            return;
        }

        forkJoin([
            // TODO: Add content saving [@hamza]
            this._updateStoreLocatorOrganizationStyleConfiguration(organizationId),
        ]).subscribe({
            next: (success) => {
                this.isSavingChanges.set(false);
                if (success) {
                    const updatedStyles = this.organizationStyleConfiguration()?.toDto();
                    if (updatedStyles) {
                        const organizationConfiguration = this._storeLocatorContext.storeLocatorOrganizationConfiguration();
                        if (!organizationConfiguration) {
                            return;
                        }
                        organizationConfiguration.updateStyles({
                            storePageStyles: updatedStyles.store,
                            centralizationPageStyles: updatedStyles.centralization,
                        });
                    }
                    this._toastService.openSuccessToast(this._translateService.instant('store_locator.edit_modal.save_as_draft.success'));
                    this.dialogRef()?.close();
                }
            },
            error: (error) => {
                this.isSavingChanges.set(false);
                console.error('Error saving store locator pages as draft:', error);
                this._toastService.openErrorToast(this._translateService.instant('store_locator.edit_modal.save_as_draft.fail'));
            },
        });
    }

    private _updateStoreLocatorOrganizationStyleConfiguration(organizationId: string): Observable<boolean> {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();
        const updateBody = organizationStyleConfiguration?.toDto();
        if (isNil(updateBody)) {
            return of(true);
        }

        return this._storeLocatorService.updateOrganizationConfigurationPages(organizationId, updateBody);
    }
}
