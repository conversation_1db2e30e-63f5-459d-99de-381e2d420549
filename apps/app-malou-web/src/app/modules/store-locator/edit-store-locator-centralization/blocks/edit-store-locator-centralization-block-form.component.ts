import { NgC<PERSON>, NgTemplateOutlet } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    inject,
    input,
    signal,
    TemplateRef,
    WritableSignal,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { isEqual } from 'lodash';

import { MimeType, StoreLocatorCentralizationPageElementIds } from '@malou-io/package-utils';

import { EditStoreLocatorCentralizationContext } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.context';
import { StoreLocatorCentralizationBlockType } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.interface';
import { StoreLocatorInputType } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { EXTRA_COLORS } from ':modules/store-locator/shared/edit-store-locator/edit-store-locator.interface';
import { parseConfigurationStyleClassesToCssStyle } from ':modules/store-locator/shared/edit-store-locator/utils/edit-store-locator-page.utils';
import { ImageUploaderSource } from ':shared/components/image-uploader/image-uploader.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

enum BlockFromTabs {
    CONTENT,
    STYLE,
}
@Component({
    selector: 'app-edit-store-locator-centralization-block-form-wrapper',
    templateUrl: './edit-store-locator-centralization-block-form.component.html',
    styleUrls: ['./edit-store-locator-centralization-block-form.component.scss'],
    imports: [MatButtonModule, MatTabsModule, TranslateModule, NgTemplateOutlet, MatIconModule, NgClass],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorCentralizationBlockFormComponent {
    readonly contentFormTemplate = input<TemplateRef<any>>();
    readonly styleFormTemplate = input<TemplateRef<any>>();

    readonly destroyRef = inject(DestroyRef);
    readonly editStoreLocatorCentralizationContext = inject(EditStoreLocatorCentralizationContext);

    readonly organizationRestaurants = computed(() => this.editStoreLocatorCentralizationContext.organizationRestaurants());
    readonly organizationStyleConfiguration = computed(() => this.editStoreLocatorCentralizationContext.organizationStyleConfiguration());
    readonly colorOptions = computed(() => [
        ...(this.editStoreLocatorCentralizationContext.organizationStyleConfiguration()?.colors.map((color) => color.value) ?? []),
        ...EXTRA_COLORS,
    ]);

    readonly storeCentralizationPageState = computed(() => this.editStoreLocatorCentralizationContext.storeCentralizationPageState());

    readonly selectedTabIndex: WritableSignal<number> = signal(BlockFromTabs.CONTENT);

    readonly SvgIcon = SvgIcon;
    readonly MimeType = MimeType;
    readonly ImageUploaderSource = ImageUploaderSource;

    readonly isBlockInError = computed(() => this.editStoreLocatorCentralizationContext.isBlockInError().isError);
    readonly shouldDisableModal = computed(() => this.editStoreLocatorCentralizationContext.shouldDisableModal());

    handleTabChange(event: number): void {
        this.selectedTabIndex.set(event);
    }

    protected getStyleMap(elementIds: StoreLocatorCentralizationPageElementIds[]): Record<string, Record<string, string>> {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();
        if (!organizationStyleConfiguration) {
            return {};
        }

        const styleMap: Record<string, Record<string, string>> = {};

        elementIds.forEach((key) => {
            styleMap[key] = parseConfigurationStyleClassesToCssStyle(
                organizationStyleConfiguration.getCentralizationPageElementStyle(key) as string[],
                {
                    colors: organizationStyleConfiguration.colors,
                    fonts: organizationStyleConfiguration.fonts,
                }
            );
        });

        return styleMap;
    }

    protected trackEditContentActivity({
        block,
        element,
    }: {
        block: StoreLocatorCentralizationBlockType;
        element: StoreLocatorInputType;
    }): void {
        this.editStoreLocatorCentralizationContext.editStoreLocatorActivityTracker()?.trackEditCentralizationContentActivity({
            block,
            element,
        });
    }

    protected trackEditStyleActivity({
        block,
        changes,
    }: {
        block: StoreLocatorCentralizationBlockType;
        changes: Record<string, string[]>;
    }): void {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();
        if (organizationStyleConfiguration) {
            Object.keys(changes).forEach((elementId: StoreLocatorCentralizationPageElementIds) => {
                if (this.isElementStyleEditTracked(elementId)) {
                    return;
                }
                if (
                    !isEqual(
                        organizationStyleConfiguration.getCentralizationPageElementStyle(
                            elementId as StoreLocatorCentralizationPageElementIds
                        ),
                        changes[elementId]
                    )
                ) {
                    this.editStoreLocatorCentralizationContext.editStoreLocatorActivityTracker()?.trackEditCentralizationStyleActivity({
                        block,
                        elementId,
                    });
                }
            });
        }
    }

    protected isElementTracked(element: StoreLocatorInputType, block: StoreLocatorCentralizationBlockType): boolean {
        return (
            this.editStoreLocatorCentralizationContext
                .editStoreLocatorActivityTracker()
                ?.isElementContentEditTrackedForCentralizationPage({ block, element }) || false
        );
    }

    protected isElementStyleEditTracked(elementId: StoreLocatorCentralizationPageElementIds): boolean {
        return (
            this.editStoreLocatorCentralizationContext
                .editStoreLocatorActivityTracker()
                ?.isCentralizationElementStyleEditTracked(elementId) || false
        );
    }
}
