<app-edit-store-locator-centralization-block-form-wrapper
    [contentFormTemplate]="contentFormTemplate"
    [styleFormTemplate]="styleFormTemplate" />

<ng-template #contentFormTemplate>
    <div class="flex flex-col gap-5">
        <div class="flex flex-col gap-3 rounded-[10px] !bg-malou-color-background-light">
            <ng-container
                [ngTemplateOutlet]="controlWrapperTemplate"
                [ngTemplateOutletContext]="{
                    onClick: toggleControlPanelExpanded.bind(this, MapBlockControlType.PIN),
                    isPanelExpanded: getControlPanelExpanded(MapBlockControlType.PIN),
                    title: 'store_locator.edit_centralization_modal.controls.image.header' | translate,
                    controlTemplate: activePinControlTemplate,
                    isTitleControl: false,
                }"></ng-container>

            <div class="border border-malou-color-background-dark"></div>
        </div>
    </div>
</ng-template>

<ng-template #styleFormTemplate>
    <form class="flex flex-col gap-5" [formGroup]="styleForm">
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="search">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit_centralization_modal.style.search' | translate }}
            </div>
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('search.backgroundColor')" />

            <app-store-locator-edit-page-radius-slider
                [title]="'store_locator.edit_centralization_modal.style.input_radius' | translate"
                [control]="styleForm.get('search.radius')"></app-store-locator-edit-page-radius-slider>

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.input_border' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('search.inputBorderColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.input_background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('search.inputBackgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.button' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('search.buttonBackgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.button_border' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('search.buttonBorderColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.button_icon' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('search.buttonIconColor')" />
        </div>

        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="item">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit_centralization_modal.style.restaurant_block' | translate }}
            </div>

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('item.wrapperBackgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.block_background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('item.itemBackgroundColor')" />

            <app-store-locator-edit-page-radius-slider
                [title]="'store_locator.edit_centralization_modal.style.block_radius' | translate"
                [control]="styleForm.get('item.radius')"></app-store-locator-edit-page-radius-slider>

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.block_text' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('item.textColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.icon' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('item.iconColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.selection_bar' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('item.selectedBarColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.distance_block_background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('item.distanceButtonBackgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.distance_block_text' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('item.distanceButtonTextColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.opening_soon_background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('item.nextOpeningBlockBackgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_centralization_modal.style.opening_soon_text' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('item.nextOpeningBlockTextColor')" />
        </div>
    </form>
</ng-template>

<ng-template #activePinControlTemplate>
    <div class="mb-3 w-fit">
        <app-image-uploader
            titleClass="!malou-text-14--bold !text-malou-color-text-1"
            [media]="uploadedActivePin()"
            [acceptedMimeTypes]="[MimeType.IMAGE_PNG, MimeType.IMAGE_JPEG]"
            [allowedSources]="[ImageUploaderSource.COMPUTER]"
            [title]="''"
            [restaurant]="restaurant()"
            [wrapperClass]="'!p-0'"
            (onMediaSelected)="onMediaSelected($event, true)"></app-image-uploader>
    </div>
    <div class="flex flex-col">
        <div class="mb-3 flex items-center">
            <app-slide-toggle class="mr-3" [checked]="isUploadInactivePinActive()" (onToggle)="onToggle()"> </app-slide-toggle>
            <div class="font-regular text-[12px] text-malou-color-text-1">
                {{ 'store_locator.edit_centralization_modal.controls.image.add_another_pin' | translate }}
            </div>
        </div>
        <div class="w-fit">
            @if (isUploadInactivePinActive()) {
                <app-image-uploader
                    titleClass="!malou-text-14--bold !text-malou-color-text-1"
                    [media]="uploadedInactivePin()"
                    [restaurant]="restaurant()"
                    [acceptedMimeTypes]="[MimeType.IMAGE_PNG, MimeType.IMAGE_JPEG]"
                    [allowedSources]="[ImageUploaderSource.COMPUTER]"
                    [title]="''"
                    [wrapperClass]="'!p-0'"
                    (onMediaSelected)="onMediaSelected($event, false)"></app-image-uploader>
            }
        </div>
    </div>
</ng-template>

<ng-template
    let-onClick="onClick"
    let-title="title"
    let-controlTemplate="controlTemplate"
    let-isPanelExpanded="isPanelExpanded"
    #controlWrapperTemplate>
    <div class="expansion-header malou-expansion-panel px-5">
        <mat-accordion>
            <mat-expansion-panel class="!border-none" hideToggle [expanded]="false">
                <mat-expansion-panel-header class="!pl-0" (click)="void 0">
                    <div class="flex w-full items-center justify-between">
                        <div class="malou-text-13--bold text-malou-color-text-1">
                            {{ title }}
                        </div>

                        <div class="flex items-center">
                            <mat-icon
                                class="!w-3 transition-all"
                                color="primary"
                                [svgIcon]="SvgIcon.CHEVRON_DOWN"
                                [class.rotate-180]="isPanelExpanded"
                                (click)="onClick()"></mat-icon>
                        </div>
                    </div>
                </mat-expansion-panel-header>

                <ng-template matExpansionPanelContent>
                    <div class="flex flex-col gap-2 !bg-malou-color-background-light">
                        <ng-container [ngTemplateOutlet]="controlTemplate"></ng-container>
                    </div>
                </ng-template>
            </mat-expansion-panel>
        </mat-accordion>
    </div>
</ng-template>
