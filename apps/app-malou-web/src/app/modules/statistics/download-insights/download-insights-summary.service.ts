import { inject, Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { DateTime } from 'luxon';
import { catchError, filter, forkJoin, map, of, switchMap, take } from 'rxjs';

import { aggregatedInsightsTabs, CsvInsightChart, HeapEventName, InsightsTab, MalouComparisonPeriod } from '@malou-io/package-utils';

import { UsersContext } from ':core/context/users.context';
import { HeapService } from ':core/services/heap.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ToastService } from ':core/services/toast.service';
import { selectFilters as selectAggregatedFilters } from ':modules/aggregated-statistics/store/aggregated-statistics.selectors';
import { DownloadCsvInsightsSummaryService } from ':modules/statistics/download-insights/download-csv-insights-summary.service';
import { selectFilters } from ':modules/statistics/store/statistics.selectors';
import { DownloadInsightsFooterPopinComponent } from ':shared/components/download-insights-modal/download-insights-footer-popin/download-insights-footer-popin.component';
import { DownloadFormat, FileExtension } from ':shared/components/download-insights-modal/download-insights.interface';
import { FooterPopinService } from ':shared/components/footer-popin/footer-popin.service';
import { downloadFilesAsZip } from ':shared/helpers/download-files-as-zip';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';

@Injectable({ providedIn: 'root' })
export class DownloadInsightsSummaryService {
    private readonly _footerPopinService = inject(FooterPopinService);
    private readonly _downloadCsvInsightsSummaryService = inject(DownloadCsvInsightsSummaryService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _enumTranslatePipe = inject(EnumTranslatePipe);
    private readonly _restaurantService = inject(RestaurantsService);
    private readonly _heapService = inject(HeapService);
    private readonly _usersContext = inject(UsersContext);
    private readonly _store = inject(Store);

    downloadCsvInsightsSummary({ insightsTab, csvChart }: { insightsTab: InsightsTab; csvChart: CsvInsightChart }): void {
        const footerId = this.showFooter();
        this._store
            .select(selectFilters)
            .pipe(
                take(1),
                filter((filters) => !!filters.dates && !!filters.comparisonPeriod && !!filters.isFiltersLoaded),
                map((filters) => ({
                    startDate: filters.dates.startDate,
                    endDate: filters.dates.endDate,
                    comparisonPeriod: filters.comparisonPeriod,
                })),
                switchMap(({ comparisonPeriod, endDate, startDate }) =>
                    forkJoin({
                        startDate: of(startDate),
                        endDate: of(endDate),
                        csvStringData: this._downloadCsvInsightsSummaryService.getCsvInsightsSummaryData$({
                            csvChart,
                            options: {
                                startDate: startDate!,
                                endDate: endDate!,
                                comparisonPeriod,
                            },
                        }),
                    })
                ),
                map(({ csvStringData, startDate, endDate }) => {
                    if (!csvStringData || !csvStringData.length) {
                        return null;
                    }
                    this._generatedCsvFiles({
                        csvStringData,
                        footerId,
                        insightsTab,
                        csvChart,
                        startDate,
                        endDate,
                    });
                }),
                catchError((err) => {
                    console.error('Error downloading insights summary:', err);
                    this.hideFooter(footerId);
                    this._toastService.openErrorToast(this._translateService.instant('common.unknown_error'));
                    return [];
                })
            )
            .subscribe();
    }

    downloadAggregatedCsvInsightsSummary({ insightsTab, csvChart }: { insightsTab: InsightsTab; csvChart: CsvInsightChart }): void {
        const footerId = this.showFooter();
        this._store
            .select(selectAggregatedFilters)
            .pipe(
                take(1),
                filter((filters) => !!filters.dates && !!filters.restaurantIds),
                map((filters) => ({
                    startDate: filters.dates.startDate,
                    endDate: filters.dates.endDate,
                })),
                switchMap(({ endDate, startDate }) =>
                    forkJoin({
                        startDate: of(startDate),
                        endDate: of(endDate),
                        csvStringData: this._downloadCsvInsightsSummaryService.getCsvInsightsSummaryData$({
                            csvChart,
                            options: {
                                startDate: startDate!,
                                endDate: endDate!,
                                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                            },
                        }),
                    })
                ),
                map(({ csvStringData, startDate, endDate }) => {
                    if (!csvStringData || !csvStringData.length) {
                        console.error('No data available for the selected period.');
                        this.hideFooter(footerId);
                        return null;
                    }
                    this._generatedCsvFiles({
                        csvStringData,
                        footerId,
                        insightsTab,
                        csvChart,
                        startDate,
                        endDate,
                    });
                }),
                catchError((err) => {
                    console.error('Error downloading insights summary:', err);
                    this.hideFooter(footerId);
                    this._toastService.openErrorToast(this._translateService.instant('common.unknown_error'));
                    return [];
                })
            )
            .subscribe();
    }

    showFooter(): string {
        return this._footerPopinService.open(DownloadInsightsFooterPopinComponent, {});
    }

    hideFooter(footerId: string): void {
        this._footerPopinService.close(footerId);
    }

    private _generatedCsvFiles({
        csvStringData,
        footerId,
        insightsTab,
        csvChart,
        startDate,
        endDate,
    }: {
        csvStringData: string;
        footerId: string;
        insightsTab: InsightsTab;
        csvChart: CsvInsightChart;
        startDate: Date | null;
        endDate: Date | null;
    }): void | null {
        const res = new Blob([csvStringData], { type: 'text/csv;charset=UTF-8' });

        this.hideFooter(footerId);
        if (!res) {
            this._toastService.openErrorToast(this._translateService.instant('common.error'));
            return;
        }

        const suffix = this._getDatesSuffix(startDate!, endDate!);
        const zipFilename = this._getInsightTabFilename(insightsTab, suffix, FileExtension.ZIP);
        void downloadFilesAsZip([res], [this._getCsvInsightsChartFilename(csvChart, suffix, FileExtension.CSV)], zipFilename);

        this.hideFooter(footerId);

        this._heapService.track(HeapEventName.DOWNLOAD_INSIGHTS, {
            tab: insightsTab,
            restaurantIds: this._getRestaurantIdsForHeapEvent(),
            userEmail: this._usersContext.currentUser()?.email ?? '',
            userId: this._usersContext.currentUser()?.id ?? '',
            charts: [csvChart],
            format: DownloadFormat.CSV,
        });
    }

    private _getInsightTabFilename(tab: InsightsTab, suffix: string, extension: string): string {
        const base = this._enumTranslatePipe.transform(tab, 'insights_tab_filename');
        if (aggregatedInsightsTabs.includes(tab)) {
            return `${base}${suffix}.${extension}`;
        } else {
            const restaurantName = this._restaurantService.currentRestaurant?.getDisplayName().replace(/ /g, '_') ?? '';
            return `${base}_${restaurantName}${suffix}.${extension}`;
        }
    }

    private _getCsvInsightsChartFilename(chart: CsvInsightChart, suffix: string, extension: string): string {
        const base = this._enumTranslatePipe.transform(chart, 'csv_insights_chart_filename');
        if (chart === CsvInsightChart.AGGREGATED_INSIGHTS_SUMMARY) {
            return `${base}${suffix}.${extension}`;
        }
        const restaurantName = this._restaurantService.currentRestaurant?.getDisplayName().replace(/ /g, '_') ?? '';
        return `${base}_${restaurantName}${suffix}.${extension}`;
    }

    private _getDatesSuffix(startDate: Date, endDate: Date): string {
        const startDateFormatted = DateTime.fromJSDate(startDate).toISODate();
        const endDateFormatted = DateTime.fromJSDate(endDate).toISODate();
        return `_${startDateFormatted}_${endDateFormatted}`;
    }

    private _getRestaurantIdsForHeapEvent(): string {
        // todo: to add later when we have the multi restaurant feature [@hamza]
        // const restaurantIds = this.data.filters.restaurantIds;
        // if (restaurantIds && restaurantIds.length > 0) {
        //     return restaurantIds.join(',');
        // }
        return this._restaurantService.currentRestaurant?._id ?? '';
    }
}
