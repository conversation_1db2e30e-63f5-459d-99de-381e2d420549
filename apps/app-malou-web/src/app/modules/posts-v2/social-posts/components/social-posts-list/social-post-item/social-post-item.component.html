@let userCanManagePost = CaslAction.MANAGE | caslAble: CaslSubject.SOCIAL_POST;

<div
    class="flex overflow-hidden rounded-[5px] border border-malou-color-border-primary bg-malou-color-background-white transition-all duration-500"
    [ngClass]="{ 'border-[4px] border-malou-color-purple--light': isHighlighted() }">
    <div class="min-h-[105px] w-[105px] shrink-0 self-stretch">
        <app-social-post-media-item
            [ngClass]="{ 'cursor-pointer': post() | applySelfPure: 'canEdit' }"
            [media]="post().media"
            [icon]="post() | applySelfPure: 'getPostTypeIcon'"
            [link]="post().socialLink"
            [tag]="feedbackCountTag()"
            [isReadonly]="isReadonly()"
            (refreshMedia)="onRefreshPost()"
            (tagClick)="onFeedbackCountTagClick($event)"
            (click)="onUpdatePost()"></app-social-post-media-item>
    </div>

    <div class="flex min-w-0 grow flex-col gap-3 px-3 pb-1 pt-2">
        <app-post-item-header
            [sortedPlatformKeys]="postSortedPlatformKeys()"
            [published]="post().published"
            [isPublishing]="post().isPublishing"
            [postDate]="post() | applySelfPure: 'getPostDate'"
            [authorInitials]="authorInitials()"
            [authorName]="authorName()"
            [canSchedule]="post() | applySelfPure: 'canSchedule'"
            [showActionsButton]="
                (post() | applySelfPure: 'canOpenSocialLink') ||
                (post() | applySelfPure: 'canEdit') ||
                (post() | applySelfPure: 'canDelete') ||
                (post() | applySelfPure: 'canDuplicate')
            "
            [isReadonly]="isReadonly()"
            [userCanManagePost]="userCanManagePost"
            [actions]="actions()"
            (postDateChange)="onPostDateChange($event)"></app-post-item-header>

        @if (post().published === PostPublicationStatus.ERROR) {
            <ng-container [ngTemplateOutlet]="errorTemplate"></ng-container>
        } @else {
            <div class="flex flex-col gap-1">
                @if (post().title; as title) {
                    <div class="malou-text-11--semibold italic text-malou-color-text-2">{{ title }}</div>
                }
                <app-text-with-see-more
                    textClass="leading-5 text-malou-color-text-2 italic malou-text-11--regular"
                    [text]="textAndHashtags()"
                    [rowCount]="2"></app-text-with-see-more>
            </div>
        }
    </div>
</div>

<ng-template #errorTemplate>
    @let mostRecentPublicationErrorCode = post().mostRecentPublicationErrorCode ?? PublicationErrorCode.UNKNOWN_ERROR;
    <div class="w-fit rounded-md bg-malou-color-background-error px-3 py-1 italic">
        <span class="malou-text-10--regular text-malou-color-state-error">{{
            mostRecentPublicationErrorCode
                | enumTranslate: 'publication_error_code' : undefined : { platformKey: post().platformKeys[0].toString() }
        }}</span>
        <span
            class="malou-text-10--semibold ml-1 text-malou-color-primary"
            [ngClass]="{ 'cursor-pointer': !isReadonly() }"
            [id]="'tracking_social_posts_error_cta_' + (mostRecentPublicationErrorCode | lowercase)"
            (click)="!isReadonly() && onErrorCtaClick(mostRecentPublicationErrorCode)">
            {{
                mostRecentPublicationErrorCode
                    | enumTranslate: 'publication_error_code_cta' : undefined : { platformKey: post().platformKeys[0].toString() }
            }}
        </span>
    </div>
</ng-template>
