import { SocialPostMediaDto } from '@malou-io/package-dto';
import { MediaType, PlatformKey, PostInsightEntityType, PostType } from '@malou-io/package-utils';

enum PostInsightMetric {
    LIKES = 'likes',
    COMMENTS = 'comments',
    SHARES = 'shares',
    SAVED = 'saved',
    ENGAGEMENT_RATE = 'engagementRate',
    REACH = 'reach',
    IMPRESSIONS = 'impressions',
    PLAYS = 'plays',
}
export interface PostInsightProps {
    id: string;
    platformKey: PlatformKey;
    socialId: string;
    entityType: PostInsightEntityType;
    platformSocialId: string;
    postSocialCreatedAt: string;
    followersCountAtPostTime: number | null;
    data: {
        impressions: number;
        likes: number;
        comments: number;
        shares: number;
        reach: number | null;
        plays: number | null;
        saved: number | null;
    };
    post: {
        id: string;
        text: string;
        postType: PostType;
        socialLink?: string;
        attachment: {
            socialId: string | null;
            thumbnailUrl: string | null;
            type: MediaType | undefined;
            urls: {
                original: string;
            };
        } | null;
    };
}

export class PostInsight {
    id: string;
    platformKey: PlatformKey;
    socialId: string;
    entityType: PostInsightEntityType;
    platformSocialId: string;

    impressions: number;
    likes: number;
    comments: number;
    shares: number;
    reach: number | null;
    plays: number | null;
    saved: number | null;

    postId: string;
    caption: string;
    postCreatedAt: Date;
    postType: PostType;
    permalink: string;
    thumbnail: string;
    firstAttachmentType: MediaType | undefined;
    mediaUrl: string;

    followersCountAtPostTime: number | null;
    engagementRate: number | null;

    constructor(data: PostInsightProps) {
        this.id = data.id;
        this.platformKey = data.platformKey;
        this.socialId = data.socialId;
        this.entityType = data.entityType;
        this.platformSocialId = data.platformSocialId;

        this.impressions = data.data.impressions;
        this.likes = data.data.likes;
        this.comments = data.data.comments;
        this.shares = data.data.shares;
        this.reach = data.data.reach;
        this.plays = data.data.plays;
        this.saved = data.data.saved;

        this.postId = data.post.id;
        this.caption = data.post.text;
        this.postCreatedAt = new Date(data.postSocialCreatedAt);
        this.postType = data.post.postType;
        this.mediaUrl = data.post.attachment?.urls.original || '';
        this.thumbnail = data.post.attachment?.thumbnailUrl || data.post.attachment?.urls.original || '';
        this.permalink = data.post.socialLink || '';
        this.firstAttachmentType = data.post.attachment?.type;

        this.followersCountAtPostTime = data.followersCountAtPostTime ?? null;
        this.engagementRate = this._getEngagementRate();
    }

    isPlatformMissingProperty = (metric: PostInsightMetric): boolean => {
        const nonExistentMetricsByPlatforms: Partial<Record<PlatformKey, PostInsightMetric[]>> = {
            [PlatformKey.INSTAGRAM]: [],
            [PlatformKey.FACEBOOK]: [PostInsightMetric.SAVED],
            [PlatformKey.TIKTOK]: [PostInsightMetric.REACH, PostInsightMetric.SAVED],
        };
        return nonExistentMetricsByPlatforms[this.platformKey]?.includes(metric) ?? false;
    };

    isInstagramCarrousel = (): boolean => this.platformKey === PlatformKey.INSTAGRAM && this.postType === PostType.CAROUSEL;

    getEngagement(): number {
        return this.likes + this.comments + (this.saved ?? 0) + this.shares;
    }

    refreshMediaInfo(mediaInfo: SocialPostMediaDto | null): void {
        if (!mediaInfo) {
            return;
        }
        this.mediaUrl = mediaInfo.url;
        this.firstAttachmentType = mediaInfo.type;
        this.thumbnail = mediaInfo.thumbnailUrl || mediaInfo.url;
    }

    private _getEngagementRate(): number | null {
        if (this.followersCountAtPostTime === null) {
            return null;
        }
        const engagement = this.getEngagement();
        return (engagement / this.followersCountAtPostTime) * 100;
    }
}
