import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { isNil, round } from 'lodash';

import { TimeInMilliseconds, TimeInMinutes } from '@malou-io/package-utils';

import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import {
    AggregatedSummarySectionsData,
    AggregatedSummarySectionsTranslationKeys,
} from ':shared/services/csv-services/aggregated-insights/aggregated-summary/aggregated-summary.interface';
import { CsvAsStringArrays } from ':shared/services/csv-services/csv-service.abstract';
import { SummarySection } from ':shared/services/csv-services/shared/summary/summary.interface';

const DEFAULT_CSV_EMPTY_VALUE = 'N/A';
const DEFAULT_EVOLUTION_VALUE = '-';
@Injectable({ providedIn: 'root' })
export class AggregatedDataToCsvRowMapperService {
    constructor(
        private readonly _enumTranslatePipe: EnumTranslatePipe,
        private readonly _translate: TranslateService
    ) {}

    mapSeoSectionDataToCsvRows(
        currentData: AggregatedSummarySectionsData['seo'],
        previousData: AggregatedSummarySectionsData['seo']
    ): CsvAsStringArrays {
        if (!currentData) {
            return [[]];
        }

        const impressionRows = Object.entries(currentData.impressions).map(([key, value]) => {
            if (key === 'totalAppearance') {
                return [
                    this._getRowNameFromKey(`impressions.${key}`, SummarySection.SEO),
                    this._formatNumber(value),
                    this._formatNumber(previousData?.impressions[key]),
                    this._formatNumber(this._computeEvolution(value, previousData?.impressions[key], { isPercentageOfPrevious: true }), {
                        isPercentage: true,
                        shouldRound: true,
                        decimalPlaces: 2,
                    }),
                ];
            }
            return [
                this._getRowNameFromKey(`impressions.${key}`, SummarySection.SEO),
                this._formatNumber(value),
                this._formatNumber(previousData?.impressions[key]),
                this._formatNumber(this._computeEvolution(value, previousData?.impressions[key])),
            ];
        });

        const actionRows = Object.entries(currentData.actions).map(([key, value]) => {
            if (key === 'totalActions') {
                return [
                    this._getRowNameFromKey(`actions.${key}`, SummarySection.SEO),
                    this._formatNumber(value),
                    this._formatNumber(previousData?.actions[key]),
                    this._formatNumber(this._computeEvolution(value, previousData?.actions[key], { isPercentageOfPrevious: true }), {
                        isPercentage: true,
                        shouldRound: true,
                        decimalPlaces: 2,
                    }),
                ];
            }
            if (key === 'conversionRate') {
                return [
                    this._getRowNameFromKey(`actions.${key}`, SummarySection.SEO),
                    this._formatNumber(value, { isPercentage: true, shouldRound: true, decimalPlaces: 2 }),
                    this._formatNumber(previousData?.actions[key], { isPercentage: true, shouldRound: true, decimalPlaces: 2 }),
                    this._formatNumber(this._computeEvolution(value, previousData?.actions[key]), {
                        isPercentageEvolution: true,
                        shouldRound: true,
                        decimalPlaces: 2,
                    }),
                ];
            }
            return [
                this._getRowNameFromKey(`actions.${key}`, SummarySection.SEO),
                this._formatNumber(value),
                this._formatNumber(previousData?.actions[key]),
                this._formatNumber(this._computeEvolution(value, previousData?.actions[key])),
            ];
        });

        const otherRows = [
            [
                this._getRowNameFromKey('keywordsInTop20Count', SummarySection.SEO),
                this._formatNumber(currentData.keywordsInTop20Count),
                this._formatNumber(previousData?.keywordsInTop20Count),
                this._formatNumber(this._computeEvolution(currentData.keywordsInTop20Count, previousData?.keywordsInTop20Count)),
            ],
            [
                this._getRowNameFromKey('totalNotorietySearchCount', SummarySection.SEO),
                this._formatNumber(currentData.totalNotorietySearchCount),
                this._formatNumber(previousData?.totalNotorietySearchCount),
                this._formatNumber(this._computeEvolution(currentData.totalNotorietySearchCount, previousData?.totalNotorietySearchCount)),
            ],
            [
                this._getRowNameFromKey('totalDiscoverySearchCount', SummarySection.SEO),
                this._formatNumber(currentData.totalDiscoverySearchCount),
                this._formatNumber(previousData?.totalDiscoverySearchCount),
                this._formatNumber(this._computeEvolution(currentData.totalDiscoverySearchCount, previousData?.totalDiscoverySearchCount)),
            ],
        ];

        const googlePostCountRow = [
            [
                this._getRowNameFromKey('googlePostCount', 'seo'),
                this._formatNumber(currentData.googlePostCount),
                this._formatNumber(previousData?.googlePostCount),
                this._formatNumber(this._computeEvolution(currentData.googlePostCount, previousData?.googlePostCount)),
            ],
        ];

        const top3AppearancesRows =
            currentData.top3Appearances?.map((keyword, index) => [
                this._getRowNameFromKey('top_appearances', 'seo', { index: `${index + 1}` }),
                keyword,
                previousData?.top3Appearances?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        const flop3AppearancesRows =
            currentData.flop3Appearances?.map((keyword, index) => [
                this._getRowNameFromKey('flop_appearances', 'seo', { index: `${index + 1}` }),
                keyword,
                previousData?.flop3Appearances?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        const top3ActionsRows =
            currentData.top3Actions?.map((keyword, index) => [
                this._getRowNameFromKey('top_actions', 'seo', { index: `${index + 1}` }),
                keyword,
                previousData?.top3Actions?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        const flop3ActionsRows =
            currentData.flop3Actions?.map((keyword, index) => [
                this._getRowNameFromKey('flop_actions', 'seo', { index: `${index + 1}` }),
                keyword,
                previousData?.flop3Actions?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        return [
            [this._getRowNameFromKey('seo', SummarySection.SEO), '-', '-', '-'],
            ...impressionRows,
            ...actionRows,
            ...otherRows,
            ...googlePostCountRow,
            ...top3AppearancesRows,
            ...flop3AppearancesRows,
            ...top3ActionsRows,
            ...flop3ActionsRows,
        ];
    }

    mapEReputationSectionDataToCsvRows(
        currentData: AggregatedSummarySectionsData['eReputation'],
        previousData: AggregatedSummarySectionsData['eReputation']
    ): CsvAsStringArrays {
        if (!currentData) {
            return [[]];
        }

        const reviewsRows = [
            [
                this._getRowNameFromKey('reviews.totalCount', SummarySection.E_REPUTATION),
                this._formatNumber(currentData.reviews.totalCount),
                this._formatNumber(previousData?.reviews.totalCount),
                this._formatNumber(this._computeEvolution(currentData.reviews.totalCount, previousData?.reviews.totalCount)),
            ],
            [
                this._getRowNameFromKey('reviews.averageRating', SummarySection.E_REPUTATION),
                this._formatNumber(currentData.reviews.averageRating, { shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(previousData?.reviews.averageRating, { shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(this._computeEvolution(currentData.reviews.averageRating, previousData?.reviews.averageRating), {
                    shouldRound: true,
                    decimalPlaces: 2,
                }),
            ],
        ];

        const notesRows = currentData.notes
            .filter((platformNote) => platformNote.note !== 1)
            .map(({ platform, note }) => [
                this._getRowNameFromKey('average_rating', SummarySection.E_REPUTATION, {
                    platform: this._enumTranslatePipe.transform(platform, 'platform_key'),
                }),
                this._formatNumber(note, { shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(previousData?.notes.find((n) => n.platform === platform)?.note, { shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(this._computeEvolution(note, previousData?.notes.find((n) => n.platform === platform)?.note), {
                    shouldRound: true,
                    decimalPlaces: 2,
                }),
            ]);

        const sentimentsPercentageRows = [
            [
                this._getRowNameFromKey('sentimentsPercentage.positive', SummarySection.E_REPUTATION),
                this._formatNumber(currentData.sentimentsPercentage.positive, { isPercentage: true, shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(previousData?.sentimentsPercentage.positive, {
                    isPercentage: true,
                    shouldRound: true,
                    decimalPlaces: 2,
                }),
                this._formatNumber(
                    this._computeEvolution(currentData.sentimentsPercentage.positive, previousData?.sentimentsPercentage.positive),
                    { isPercentageEvolution: true, shouldRound: true, decimalPlaces: 2 }
                ),
            ],
            [
                this._getRowNameFromKey('sentimentsPercentage.negative', SummarySection.E_REPUTATION),
                this._formatNumber(currentData.sentimentsPercentage.negative, { isPercentage: true, shouldRound: true, decimalPlaces: 2 }),
                this._formatNumber(previousData?.sentimentsPercentage.negative, {
                    isPercentage: true,
                    shouldRound: true,
                    decimalPlaces: 2,
                }),
                this._formatNumber(
                    this._computeEvolution(currentData.sentimentsPercentage.negative, previousData?.sentimentsPercentage.negative),
                    { isPercentageEvolution: true, shouldRound: true, decimalPlaces: 2 }
                ),
            ],
        ];

        const top3GoogleRatingRows =
            currentData.top3GoogleRating?.map((subject, index) => [
                this._getRowNameFromKey('google.top', 'eReputation', { index: `${index + 1}` }),
                subject,
                previousData?.top3GoogleRating?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        const flop3GoogleRatingRows =
            currentData.flop3GoogleRating?.map((subject, index) => [
                this._getRowNameFromKey('google.flop', 'eReputation', { index: `${index + 1}` }),
                subject,
                previousData?.flop3GoogleRating?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        const top3ReviewCount =
            currentData.top3ReviewCount?.map((subject, index) => [
                this._getRowNameFromKey('reviews.top', 'eReputation', { index: `${index + 1}` }),
                subject,
                previousData?.top3ReviewCount?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];
        const flop3ReviewCount =
            currentData.flop3ReviewCount?.map((subject, index) => [
                this._getRowNameFromKey('reviews.flop', 'eReputation', { index: `${index + 1}` }),
                subject,
                previousData?.flop3ReviewCount?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        return [
            [this._getRowNameFromKey('e_reputation', SummarySection.E_REPUTATION), '-', '-', '-'],
            ...reviewsRows,
            ...sentimentsPercentageRows,
            ...notesRows,
            ...top3GoogleRatingRows,
            ...flop3GoogleRatingRows,
            ...top3ReviewCount,
            ...flop3ReviewCount,
        ];
    }

    mapBoosterSectionDataToCsvRows(
        currentData: AggregatedSummarySectionsData['booster'],
        previousData: AggregatedSummarySectionsData['booster']
    ): CsvAsStringArrays {
        if (!currentData) {
            return [[]];
        }
        const { totalTotemScanCount, totalWofScanCount, gainedPrivateReviewCount, gainedPublicReviewCount, giftCount, winnerCount } =
            currentData;

        const {
            totalTotemScanCount: previousTotalTotemScanCount,
            totalWofScanCount: previousTotalWofScanCount,
            gainedPrivateReviewCount: previousGainedPrivateReviewCount,
            gainedPublicReviewCount: previousGainedPublicReviewCount,
            giftCount: previousGiftCount,
            winnerCount: previousWinnerCount,
        } = previousData ?? {};

        const currentGeneralStatsData = {
            totalTotemScanCount,
            totalWofScanCount,
            gainedPrivateReviewCount,
            gainedPublicReviewCount,
            winnerCount,
            giftCount,
        };

        const previousGeneralStatsData = {
            totalTotemScanCount: previousTotalTotemScanCount,
            totalWofScanCount: previousTotalWofScanCount,
            gainedPrivateReviewCount: previousGainedPrivateReviewCount,
            gainedPublicReviewCount: previousGainedPublicReviewCount,
            winnerCount: previousWinnerCount,
            giftCount: previousGiftCount,
        };

        const generalStatsRows = Object.entries(currentGeneralStatsData).map(([key, value]) => [
            this._getRowNameFromKey(key, SummarySection.BOOSTER),
            this._formatNumber(value),
            this._formatNumber(previousGeneralStatsData[key]),
            this._formatNumber(this._computeEvolution(value, previousGeneralStatsData[key])),
        ]);

        const top3TotemScansRows =
            currentData.top3TotemScanCount?.map((subject, index) => [
                this._getRowNameFromKey('scans.totems.top', SummarySection.BOOSTER, { index: `${index + 1}` }),
                subject,
                previousData?.top3TotemScanCount?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        const flop3TotemScansRows =
            currentData.flop3TotemScanCount?.map((subject, index) => [
                this._getRowNameFromKey('scans.totems.flop', SummarySection.BOOSTER, { index: `${index + 1}` }),
                subject,
                previousData?.flop3TotemScanCount?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        const top3WofScansRows =
            currentData.top3WofScanCount?.map((subject, index) => [
                this._getRowNameFromKey('scans.wof.top', SummarySection.BOOSTER, { index: `${index + 1}` }),
                subject,
                previousData?.top3WofScanCount?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        const flop3WofScansRows =
            currentData.flop3WofScanCount?.map((subject, index) => [
                this._getRowNameFromKey('scans.wof.flop', SummarySection.BOOSTER, { index: `${index + 1}` }),
                subject,
                previousData?.flop3WofScanCount?.[index] ?? DEFAULT_CSV_EMPTY_VALUE,
                DEFAULT_EVOLUTION_VALUE,
            ]) ?? [];

        return [
            [this._getRowNameFromKey('booster', SummarySection.BOOSTER), '-', '-', '-'],
            ...generalStatsRows,
            ...top3TotemScansRows,
            ...flop3TotemScansRows,
            ...top3WofScansRows,
            ...flop3WofScansRows,
        ];
    }

    private _getRowNameFromKey(
        translationKey: string,
        section: AggregatedSummarySectionsTranslationKeys,
        interpolateParams?: Record<string, string>
    ): string {
        return this._enumTranslatePipe.transform(
            section,
            'csv_insights_field_names.summary',
            translationKey
                .replace(/([a-z0-9])([A-Z])/g, '$1_$2')
                .replace(/([0-9]+)([a-zA-Z])/g, '$1_$2')
                .replace(/([a-zA-Z])([0-9]+)/g, '$1_$2')
                .toLowerCase(),
            interpolateParams
        );
    }

    private _computeEvolution(
        currentValue: number | null,
        previousValue: number | null | undefined,
        options?: { isPercentageOfPrevious?: boolean }
    ): number | string | null {
        if (currentValue === null || isNil(previousValue) || isNaN(currentValue) || isNaN(previousValue)) {
            return DEFAULT_EVOLUTION_VALUE;
        }
        if (options?.isPercentageOfPrevious) {
            if (isNil(previousValue) || previousValue === 0) {
                return DEFAULT_EVOLUTION_VALUE;
            }
            return ((currentValue - previousValue) / previousValue) * 100;
        }
        return currentValue - previousValue;
    }

    private _formatNumber(
        value: number | string | null | undefined,
        options?: {
            isPercentageEvolution?: boolean;
            isPercentage?: boolean;
            shouldRound?: boolean;
            shouldRoundToTens?: boolean;
            decimalPlaces?: number;
            isTime?: boolean;
        }
    ): string {
        if (typeof value === 'string') {
            return value;
        }
        if (isNil(value) || isNaN(value)) {
            return DEFAULT_CSV_EMPTY_VALUE;
        }
        let formattedNumber = value.toString();
        if (options?.shouldRoundToTens) {
            return value >= 1000 ? `${round(value / 1000)}k €` : `${value}€`;
        }
        if (options?.isTime) {
            return this._formatTime(value);
        }
        if (options?.shouldRound) {
            formattedNumber = `${round(value, options.decimalPlaces ?? 0)}`;
        }
        if (options?.isPercentageEvolution) {
            formattedNumber = `${formattedNumber}pts`;
        }
        if (options?.isPercentage) {
            formattedNumber = `${formattedNumber}%`;
        }
        return formattedNumber;
    }

    private _formatTime(value: number): string {
        const hours = Math.floor(value / TimeInMilliseconds.HOUR);
        const minutes = Math.floor((value / TimeInMilliseconds.MINUTE) % TimeInMinutes.HOUR);

        if (hours < 1 && minutes > 0) {
            return minutes + this._translate.instant('common.minutes_short');
        }

        if (hours === 0 && minutes === 0) {
            return '0' + this._translate.instant('common.hours_short');
        }

        const min = minutes ? minutes + this._translate.instant('common.minutes_short') : '';
        return hours + this._translate.instant('common.hours_short') + ' ' + min;
    }
}
