import { inject, Injectable } from '@angular/core';
import { groupBy, partition } from 'lodash';
import { catchError, forkJoin, map, Observable, of } from 'rxjs';

import {
    AggregationTimeScale,
    getPlatformKeysWithRating,
    isNotNil,
    MalouMetric,
    PlatformKey,
    ReviewAnalysisChartDataTag,
} from '@malou-io/package-utils';

import { SegmentAnalysesService } from ':core/services/segment-analyses.service';
import { ReviewRatingInsightsByRestaurantId } from ':modules/aggregated-statistics/e-reputation/reviews/reviews-rating-kpis/review-rating-insights-by-restaurant-id/review-rating-insights-by-restaurant-id';
import { ReviewsService } from ':modules/reviews/reviews.service';
import { InsightsService } from ':modules/statistics/insights.service';
import { ReviewAnalysesChartDataByRestaurantId } from ':shared/components/review-analyses-v2/review-analyses-chart-data-by-restaurant-id/review-analyses-chart-data-by-restaurant-id';
import { ChartReviewsStatsByRestaurantId, ChartReviewsStatsDto, LightRestaurant } from ':shared/models';
import {
    AggregatedSummarySectionsData,
    AggregatedSummarySectionsDataFilters,
} from ':shared/services/csv-services/aggregated-insights/aggregated-summary/aggregated-summary.interface';
import { formatDataWithRestaurantInformation } from ':shared/services/csv-services/shared/summary/utils';

@Injectable({ providedIn: 'root' })
export class AggregatedSummaryCsvInsightsEReputationSectionService {
    private readonly _reviewsService = inject(ReviewsService);
    private readonly _insightsService = inject(InsightsService);
    private readonly _segmentAnalysesService = inject(SegmentAnalysesService);

    execute(filters: AggregatedSummarySectionsDataFilters['eReputation']): Observable<{
        current: AggregatedSummarySectionsData['eReputation'] | null;
        previous: AggregatedSummarySectionsData['eReputation'] | null;
    }> {
        const { restaurants, startDate, endDate, platformKeys, comparisonPeriodEndDate, comparisonPeriodStartDate } = filters;

        const restaurantIds = restaurants.map((r) => r.id);

        const allPlatformsWithRating = getPlatformKeysWithRating();
        const connectedPlatformsWithRating = platformKeys.filter((platform) => allPlatformsWithRating.includes(platform));

        const reviewsRequestBody = {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            platformKeys,
            restaurantIds,
            previousPeriod: false,
        };

        const ratingsRequestBody = {
            restaurantIds,
            startDate,
            endDate,
            platformKeys: connectedPlatformsWithRating,
            metrics: [MalouMetric.PLATFORM_RATING],
            aggregators: [AggregationTimeScale.BY_DAY],
        };

        const [_, localBusinessRestaurant] = partition(restaurants, (r) => r.isBrandBusiness());

        const restaurantIdsWeNeedToFetch = localBusinessRestaurant.map((r) => r.id);

        const semanticAnalysisRequestBody = {
            startDate,
            endDate,
            keys: platformKeys,
            restaurantIds: restaurantIdsWeNeedToFetch,
        };

        const emptyReviewsChartData = this._buildEmptyReviewsChartData(restaurantIds, restaurants);
        const emptySemanticAnalysisData = this._buildEmptySemanticAnalysisChartData(restaurantIdsWeNeedToFetch);

        return forkJoin([
            // Reviews
            this._reviewsService
                .getChartReviewsForUserRestaurantsV2(reviewsRequestBody)
                .pipe(map((data) => emptyReviewsChartData.merge(data))),
            this._reviewsService
                .getChartReviewsForUserRestaurantsV2({ ...reviewsRequestBody, previousPeriod: true })
                .pipe(map((data) => emptyReviewsChartData.merge(data))),

            // Ratings
            this._insightsService.getInsights(ratingsRequestBody).pipe(map((res) => ReviewRatingInsightsByRestaurantId.fromDto(res.data))),
            this._insightsService
                .getInsights({
                    ...ratingsRequestBody,
                    previousPeriod: true,
                })
                .pipe(map((res) => ReviewRatingInsightsByRestaurantId.fromDto(res.data))),

            // Segment Analysis
            this._segmentAnalysesService
                .getSegmentAnalysesChartData(semanticAnalysisRequestBody)
                .pipe(map((res) => emptySemanticAnalysisData.merge(ReviewAnalysesChartDataByRestaurantId.fromDto(res.data)))),
            this._segmentAnalysesService
                .getSegmentAnalysesChartData({
                    ...semanticAnalysisRequestBody,
                    startDate: comparisonPeriodStartDate,
                    endDate: comparisonPeriodEndDate,
                })
                .pipe(map((res) => emptySemanticAnalysisData.merge(ReviewAnalysesChartDataByRestaurantId.fromDto(res.data)))),
        ]).pipe(
            map(
                ([
                    currentReviewsData,
                    previousReviewsData,
                    currentRatingsData,
                    previousRatingsData,
                    currentSemanticAnalysisData,
                    previousSemanticAnalysisData,
                ]) => {
                    const currentReviewsComputedData = this._getReviewsData({
                        reviewsData: currentReviewsData,
                        restaurants,
                    });

                    if (!currentReviewsComputedData) {
                        return { current: null, previous: null };
                    }

                    const currentRatingsComputedData = this._getRatingData({
                        ratingData: currentRatingsData,
                        restaurants,
                    });

                    const previousReviewsComputedData = this._getReviewsData({
                        reviewsData: previousReviewsData,
                        restaurants,
                    });

                    const previousRatingsComputedData = this._getRatingData({
                        ratingData: previousRatingsData,
                        restaurants,
                    });

                    const { positiveSentimentsPercentage, negativeSentimentsPercentage } =
                        currentSemanticAnalysisData.getSentimentPercentage(ReviewAnalysisChartDataTag.TOTAL);

                    const {
                        positiveSentimentsPercentage: previousPositiveSentimentsPercentage,
                        negativeSentimentsPercentage: previousNegativeSentimentsPercentage,
                    } = previousSemanticAnalysisData.getSentimentPercentage(ReviewAnalysisChartDataTag.TOTAL);

                    return {
                        current: {
                            ...currentReviewsComputedData,
                            ...currentRatingsComputedData,
                            sentimentsPercentage: {
                                positive: positiveSentimentsPercentage,
                                negative: negativeSentimentsPercentage,
                            },
                        },
                        previous: isNotNil(previousReviewsComputedData)
                            ? {
                                  ...previousReviewsComputedData,
                                  ...previousRatingsComputedData,
                                  sentimentsPercentage: {
                                      positive: previousPositiveSentimentsPercentage,
                                      negative: previousNegativeSentimentsPercentage,
                                  },
                              }
                            : null,
                    };
                }
            ),
            catchError(() => of({ current: null, previous: null }))
        );
    }

    private _buildEmptyReviewsChartData(restaurantIds: string[], restaurants: LightRestaurant[]): ChartReviewsStatsByRestaurantId {
        const restaurantById = groupBy(restaurants, (restaurant) => restaurant.id);
        return ChartReviewsStatsByRestaurantId.fromDto(
            restaurantIds.reduce((acc, restaurantId) => {
                const restaurant = restaurantById[restaurantId]?.[0];
                acc.push({
                    restaurant: {
                        _id: restaurantId,
                        name: restaurant.name,
                        internalName: restaurant.internalName,
                        address: restaurant.address,
                    },
                    reviews: [],
                });
                return acc;
            }, [] as ChartReviewsStatsDto[])
        );
    }

    private _buildEmptySemanticAnalysisChartData(restaurantIds: string[]): ReviewAnalysesChartDataByRestaurantId {
        return ReviewAnalysesChartDataByRestaurantId.buildEmptyChartData(restaurantIds);
    }

    private _getReviewsData({
        reviewsData,
        restaurants,
    }: {
        reviewsData: ChartReviewsStatsByRestaurantId;
        restaurants: LightRestaurant[];
    }): Pick<NonNullable<AggregatedSummarySectionsData['eReputation']>, 'reviews' | 'top3ReviewCount' | 'flop3ReviewCount'> | null {
        if (reviewsData.getChartReviewStats().length === 0) {
            return null;
        }

        const data = reviewsData.getChartReviewStats().reduce(
            (acc, restaurantData) => {
                acc.reviews.totalCount += restaurantData.total ?? 0;
                acc.reviews.totalRating += restaurantData.reviews.reduce((sum, r) => sum + (r.rating ?? 0), 0);
                acc.totalReviewCountByRestaurant[restaurantData.restaurant._id] = restaurantData.total ?? 0;
                return acc;
            },
            {
                reviews: {
                    totalCount: 0,
                    totalRating: 0,
                },
                totalReviewCountByRestaurant: {} as Record<string, number>,
            }
        );

        const top3ReviewCount = Object.entries(data.totalReviewCountByRestaurant)
            .sort(([, countA], [, countB]) => countB - countA)
            .slice(0, 3)
            .map(([restaurantId, count]) => {
                const restaurant = restaurants.find((r) => r.id === restaurantId);
                return formatDataWithRestaurantInformation({ restaurant, data: count });
            });

        const flop3ReviewCount = Object.entries(data.totalReviewCountByRestaurant)
            .sort(([, countA], [, countB]) => countA - countB)
            .slice(0, 3)
            .map(([restaurantId, count]) => {
                const restaurant = restaurants.find((r) => r.id === restaurantId);
                return formatDataWithRestaurantInformation({ restaurant, data: count });
            });

        return {
            reviews: {
                averageRating: data.reviews.totalRating / (data.reviews.totalCount || 1),
                totalCount: data.reviews.totalCount,
            },
            top3ReviewCount,
            flop3ReviewCount,
        };
    }

    private _getRatingData({
        ratingData,
        restaurants,
    }: {
        ratingData: ReviewRatingInsightsByRestaurantId;
        restaurants: LightRestaurant[];
    }): Pick<NonNullable<AggregatedSummarySectionsData['eReputation']>, 'notes' | 'top3GoogleRating' | 'flop3GoogleRating'> {
        if (ratingData.getChartReviewStats().length === 0) {
            return {
                notes: [],
                top3GoogleRating: null,
                flop3GoogleRating: null,
            };
        }

        const ratingComputedData = [...ratingData.keys()].reduce(
            (acc, restaurantId) => {
                const value = ratingData.get(restaurantId);
                if (!value?.gmb?.error && value?.gmb?.by_day?.platform_rating?.length !== 0) {
                    const ratingValue = value?.gmb?.by_day?.platform_rating?.[0]?.value;
                    if (isNotNil(ratingValue)) {
                        acc.googleRatings.push({
                            restaurantId,
                            rating: ratingValue,
                        });
                    }
                }
                if (value) {
                    Object.entries(value).forEach(([platformKey, platformValue]) => {
                        if (!acc.ratings[platformKey as PlatformKey]) {
                            acc.ratings[platformKey as PlatformKey] = { restaurantCountWithPlatformData: 0, totalRating: 0 };
                        }
                        if (!platformValue.error) {
                            acc.ratings[platformKey as PlatformKey].restaurantCountWithPlatformData += 1;
                            if (platformValue.by_day?.platform_rating?.length !== 0) {
                                const ratingValue = platformValue.by_day?.platform_rating?.[0].value;
                                acc.ratings[platformKey as PlatformKey].totalRating += ratingValue ?? 0;
                            }
                        }
                    });
                }
                return acc;
            },
            {
                googleRatings: [] as { restaurantId: string; rating: number }[],
                ratings: {} as Record<PlatformKey, { restaurantCountWithPlatformData: number; totalRating: number }>,
            }
        );

        const notes = Object.entries(ratingComputedData.ratings).map(([platformKey, platformData]) => ({
            platform: platformKey as PlatformKey,
            note: platformData.totalRating / (platformData.restaurantCountWithPlatformData || 1),
        }));

        const top3GoogleRating = ratingComputedData.googleRatings
            .sort((a, b) => b.rating - a.rating)
            .slice(0, 3)
            .map(({ restaurantId, rating }) => {
                const restaurant = restaurants.find((r) => r.id === restaurantId);
                return formatDataWithRestaurantInformation({ restaurant, data: rating.toFixed(2) });
            });

        const flop3GoogleRating = ratingComputedData.googleRatings
            .sort((a, b) => a.rating - b.rating)
            .slice(0, 3)
            .map(({ restaurantId, rating }) => {
                const restaurant = restaurants.find((r) => r.id === restaurantId);
                return formatDataWithRestaurantInformation({ restaurant, data: rating.toFixed(2) });
            });

        return {
            notes,
            top3GoogleRating,
            flop3GoogleRating,
        };
    }
}
