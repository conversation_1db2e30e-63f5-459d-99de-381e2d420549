import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { DateTime } from 'luxon';
import { combineLatest, filter, forkJoin, map, Observable, of, switchMap } from 'rxjs';

import {
    getDateRangeFromMalouComparisonPeriod,
    isNotNil,
    isNotNilOrEmpty,
    MalouComparisonPeriod,
    PlatformKey,
} from '@malou-io/package-utils';

import { AggregatedStatisticsFiltersContext } from ':modules/aggregated-statistics/filters/filters.context';
import { AggregatedStatisticsState } from ':modules/aggregated-statistics/store/aggregated-statistics.interface';
import { selectFilters } from ':modules/aggregated-statistics/store/aggregated-statistics.selectors';
import { LightNfc, LightRestaurant, Restaurant } from ':shared/models';
import {
    AggregatedSummaryCsvData,
    AggregatedSummarySectionsDataFilters,
} from ':shared/services/csv-services/aggregated-insights/aggregated-summary/aggregated-summary.interface';
import { AggregatedDataToCsvRowMapperService } from ':shared/services/csv-services/aggregated-insights/aggregated-summary/data-csv-row-mapper';
import { AggregatedSummaryCsvInsightsBoosterSectionService } from ':shared/services/csv-services/aggregated-insights/aggregated-summary/sections/booster-data-section.service';
import { AggregatedSummaryCsvInsightsEReputationSectionService } from ':shared/services/csv-services/aggregated-insights/aggregated-summary/sections/e-reputation-data-section.service';
import { AggregatedSummaryCsvInsightsSeoSectionService } from ':shared/services/csv-services/aggregated-insights/aggregated-summary/sections/seo-data-section.service';
import { AbstractCsvService, CsvAsStringArrays } from ':shared/services/csv-services/csv-service.abstract';
import { SummaryCsvData } from ':shared/services/csv-services/insights/summary/summary.interface';

interface AggregatedSummaryCsvInsightsOptions {
    startDate: Date;
    endDate: Date;
}

@Injectable({ providedIn: 'root' })
export class AggregatedSummaryCsvInsightsService extends AbstractCsvService<AggregatedSummaryCsvData, AggregatedSummaryCsvInsightsOptions> {
    constructor(
        private readonly _store: Store,
        private readonly _aggregatedDataToCsvRowMapperService: AggregatedDataToCsvRowMapperService,
        private readonly _aggregatedStatisticsFiltersContext: AggregatedStatisticsFiltersContext,
        private readonly _aggregatedSummaryCsvInsightsSeoSectionService: AggregatedSummaryCsvInsightsSeoSectionService,
        private readonly _aggregatedSummaryCsvInsightsEReputationSectionService: AggregatedSummaryCsvInsightsEReputationSectionService,
        private readonly _aggregatedSummaryCsvInsightsBoosterSectionService: AggregatedSummaryCsvInsightsBoosterSectionService
    ) {
        super();
    }

    protected _isDataValid(): boolean {
        return true;
    }

    protected override _getData$(options: AggregatedSummaryCsvInsightsOptions): Observable<AggregatedSummaryCsvData> {
        const { startDate, endDate } = options;
        return combineLatest([
            this._store.select(selectFilters),
            this._aggregatedStatisticsFiltersContext.selectedRestaurants$,
            this._aggregatedStatisticsFiltersContext.selectedTotems$,
        ]).pipe(
            filter(([filters]) => isNotNilOrEmpty(filters.restaurantIds) && isNotNil(filters.dates)),
            map(([filters, selectedRestaurants, selectedTotems]: [AggregatedStatisticsState['filters'], Restaurant[], LightNfc[]]) =>
                this._getFiltersForAggregatedSummarySections({
                    restaurants: selectedRestaurants,
                    platformKeys: filters.platforms.E_REPUTATION,
                    nfcs: selectedTotems,
                    startDate,
                    endDate,
                })
            ),
            switchMap((summarySectionsFilters) =>
                forkJoin([
                    this._aggregatedSummaryCsvInsightsSeoSectionService.execute(summarySectionsFilters.seo),
                    this._aggregatedSummaryCsvInsightsEReputationSectionService.execute(summarySectionsFilters.eReputation),
                    this._aggregatedSummaryCsvInsightsBoosterSectionService.execute(summarySectionsFilters.booster),
                    of(summarySectionsFilters),
                ])
            ),
            map(([seoSectionData, eReputationData, boostersData, filters]) => {
                const formattedDates = this._getFormattedDates(filters);
                return {
                    startDate: formattedDates.startDate,
                    endDate: formattedDates.endDate,
                    previousStartDate: formattedDates.previousStartDate,
                    previousEndDate: formattedDates.previousEndDate,
                    restaurantNames: filters.restaurantNames,
                    current: {
                        seo: seoSectionData.current,
                        eReputation: eReputationData.current,
                        booster: boostersData.current,
                    },
                    previous: {
                        seo: seoSectionData.previous,
                        eReputation: eReputationData.previous,
                        booster: boostersData.previous,
                    },
                };
            })
        );
    }
    protected override _getCsvHeaderRow(data: AggregatedSummaryCsvData): CsvAsStringArrays[0] {
        return ['Data', `${data.startDate} - ${data.endDate}`, `${data.previousStartDate} - ${data.previousEndDate}`, 'Evolution'];
    }

    protected override _getCsvDataRows(data: AggregatedSummaryCsvData): CsvAsStringArrays {
        const seoRows = this._aggregatedDataToCsvRowMapperService.mapSeoSectionDataToCsvRows(data.current.seo, data.previous.seo);
        const eReputationRows = this._aggregatedDataToCsvRowMapperService.mapEReputationSectionDataToCsvRows(
            data.current.eReputation,
            data.previous.eReputation
        );
        const boosterRows = this._aggregatedDataToCsvRowMapperService.mapBoosterSectionDataToCsvRows(
            data.current.booster,
            data.previous.booster
        );
        const restaurantNames = this._formatRestaurantNameForCsv(data.restaurantNames);
        return [[restaurantNames, '', '', ''], ...seoRows, ...eReputationRows, ...boosterRows];
    }

    private _getFiltersForAggregatedSummarySections({
        restaurants,
        platformKeys,
        nfcs,
        startDate,
        endDate,
    }: {
        restaurants: Restaurant[];
        platformKeys: PlatformKey[];
        nfcs: LightNfc[];
        startDate: Date;
        endDate: Date;
    }): AggregatedSummarySectionsDataFilters {
        if (!startDate || !endDate) {
            throw new Error('Start date or end date is missing');
        }
        const startMonthYear = {
            month: startDate.getMonth() + 1,
            year: startDate.getFullYear(),
        };
        const endMonthYear = {
            month: endDate.getMonth() + 1,
            year: endDate.getFullYear(),
        };

        const { endDate: comparisonPeriodEndDate, startDate: comparisonPeriodStartDate } = getDateRangeFromMalouComparisonPeriod({
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            dateFilters: {
                startDate,
                endDate,
            },
        });

        if (!comparisonPeriodEndDate || !comparisonPeriodStartDate) {
            throw new Error('Invalid comparison period dates');
        }

        const basicFilters = {
            restaurants: restaurants.map((r) => LightRestaurant.fromRestaurant(r)),
            startDate,
            endDate,
            comparisonPeriodStartDate,
            comparisonPeriodEndDate,
        };

        return {
            restaurantNames: restaurants.map((r) => r.getDisplayName()),
            startDate,
            endDate,
            comparisonPeriodStartDate,
            comparisonPeriodEndDate,
            seo: {
                ...basicFilters,
                endDate: DateTime.fromJSDate(endDate).minus({ days: 4 }).toJSDate(),
                endMonthYear,
                startMonthYear,
            },
            eReputation: {
                ...basicFilters,
                platformKeys,
            },
            booster: {
                ...basicFilters,
                nfcs,
            },
        };
    }

    private _getFormattedDates(
        filters: AggregatedSummarySectionsDataFilters
    ): Pick<SummaryCsvData, 'startDate' | 'endDate' | 'previousStartDate' | 'previousEndDate'> {
        const { comparisonPeriodStartDate, comparisonPeriodEndDate, startDate, endDate } = filters;
        const startDateLuxon = DateTime.fromJSDate(startDate);
        const endDateLuxon = DateTime.fromJSDate(endDate);
        const comparisonPeriodStartDateLuxon = DateTime.fromJSDate(comparisonPeriodStartDate);
        const comparisonPeriodEndDateLuxon = DateTime.fromJSDate(comparisonPeriodEndDate);
        return {
            startDate: startDateLuxon.toFormat('yyyy-MM-dd'),
            endDate: endDateLuxon.toFormat('yyyy-MM-dd'),
            previousStartDate: comparisonPeriodStartDateLuxon.toFormat('yyyy-MM-dd'),
            previousEndDate: comparisonPeriodEndDateLuxon.toFormat('yyyy-MM-dd'),
        };
    }

    private _formatRestaurantNameForCsv(restaurantNames: string[]): string {
        const restaurantCount = restaurantNames.length;
        const formattedNames = [...restaurantNames];
        if (restaurantCount < 5) {
            return formattedNames.join(', ');
        }
        const numberOfBackToLineToAdd = Math.floor(restaurantCount / 5);
        for (let i = 0; i < numberOfBackToLineToAdd; i++) {
            formattedNames.splice((i + 1) * 5 + i, 0, '\n');
        }
        return formattedNames.join(', ');
    }
}
