import { DatePipe, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { PostType } from '@malou-io/package-utils';

import { SocialPostsV2Service } from ':modules/posts-v2/social-posts/social-posts.service';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';
import { SocialPostMediaComponent } from ':shared/components/social-post-media/social-post-media.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';

export interface TopPostCardInputData {
    // TODO: set as required when release-post-insights-v2 is deleted
    postId?: string;
    restaurantName?: string;
    restaurantAddress?: string;
    postType: PostType;
    platformKey: string;
    url: string;
    thumbnailUrl?: string;
    createdAt: Date;
    likes: number;
    comments: number;
    shares: number;
    saves: number;
    impressions: number;
    engagementRate: number;
    uuid?: string; // only for tracking in @for loop (template)
}

@Component({
    selector: 'app-top-post-card',
    templateUrl: './top-post-card.component.html',
    styleUrls: ['./top-post-card.component.scss'],
    imports: [
        NgTemplateOutlet,
        MatIconModule,
        TranslateModule,
        SocialPostMediaComponent,
        DatePipe,
        PlatformLogoComponent,
        ShortNumberPipe,
        ShortNumberPipe,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TopPostCardComponent {
    @Input() topPostCardInputData: TopPostCardInputData;

    private readonly _socialPostsService = inject(SocialPostsV2Service);
    readonly SvgIcon = SvgIcon;
    constructor(public readonly translateService: TranslateService) {}

    onRefreshPostMedia(): void {
        const postId = this.topPostCardInputData.postId;
        if (!postId) {
            return;
        }
        this._socialPostsService.refreshSocialPost$(postId).subscribe((result) => {
            const refreshedMedia = result.data.media;
            this.topPostCardInputData = {
                ...this.topPostCardInputData,
                url: refreshedMedia?.url || this.topPostCardInputData.url,
                thumbnailUrl: refreshedMedia?.thumbnailUrl || this.topPostCardInputData.thumbnailUrl,
            };
        });
    }
}
