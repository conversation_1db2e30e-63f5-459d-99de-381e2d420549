import { NgClass, NgTemplateOutlet } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    inject,
    input,
    model,
    output,
    Signal,
    signal,
    ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatMenuModule, MatMenuTrigger } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { filter } from 'rxjs';

import { FileFormat, getExtensionFromMimeType, isNotNil, MimeType, ProcessingMediaStatus } from '@malou-io/package-utils';

import { ProcessingMediasService } from ':core/services/processing-medias.service';
import { ToastService } from ':core/services/toast.service';
import { MediaPickerModalComponent } from ':modules/media/media-picker-modal/media-picker-modal.component';
import { MediaPickerFilter } from ':modules/media/media-picker-modal/media-picker-modal.interface';
import { MediaService, UploadV2Result } from ':modules/media/media.service';
import { StoreLocatorOrganizationRestaurant } from ':modules/store-locator/models/store-locator-organization-restaurant';
import { ImageUploaderSource } from ':shared/components/image-uploader/image-uploader.interface';
import { MediaThumbnailListComponent } from ':shared/components/media-thumbnail-list/media-thumbnail-list.component';
import { Restaurant } from ':shared/models';
import { Media } from ':shared/models/media';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { JoinPipe } from ':shared/pipes/join.pipe';
import { BodyDragAndDropEventsService } from ':shared/services/body-drag-and-drop-events.service';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

import { LoaderProgressComponent } from '../loader-progress/loader-progress.component';

type ImageMimeType = MimeType.IMAGE_PNG | MimeType.IMAGE_JPEG | MimeType.IMAGE_HEIC | MimeType.IMAGE_HEIF;

enum ImportStatus {
    SCHEDULED = 'SCHEDULED',
    UPLOADING = 'UPLOADING',
    PROCESSING = 'PROCESSING',
}

type ImportJob = { file: File } & (
    | { status: ImportStatus.SCHEDULED; progress: number | null }
    | {
          status: ImportStatus.UPLOADING;
          progress: number | null;
      }
    | { status: ImportStatus.PROCESSING; processingMediaId: string | null; progress: number | null }
);
@Component({
    selector: 'app-image-uploader',
    imports: [
        TranslateModule,
        MatIconModule,
        MatMenuModule,
        MatButtonModule,
        NgClass,
        MatIcon,
        MatMenuModule,
        TranslateModule,
        NgTemplateOutlet,
        JoinPipe,
        MediaThumbnailListComponent,
        LoaderProgressComponent,
        MatProgressSpinnerModule,
    ],
    templateUrl: './image-uploader.component.html',
    styleUrls: ['./image-uploader.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ImageUploaderComponent {
    readonly title = input<string>();
    readonly titleClass = input<string>();
    readonly wrapperClass = input<string>();
    readonly required = input<boolean>(false);
    readonly disabled = input<boolean>(false);
    readonly acceptedMimeTypes = input<ImageMimeType[]>([
        MimeType.IMAGE_PNG,
        MimeType.IMAGE_JPEG,
        MimeType.IMAGE_HEIC,
        MimeType.IMAGE_HEIF,
    ]);
    readonly showEditMediaButton = input<boolean>(true);
    readonly restaurant = input<StoreLocatorOrganizationRestaurant | Restaurant>();
    readonly uploadViaImagePreview = input<boolean>(false);
    readonly imageViewerWrapperCss = input<Record<string, any>>({});
    readonly allowedSources = input<ImageUploaderSource[]>([ImageUploaderSource.COMPUTER, ImageUploaderSource.GALLERY]);

    public readonly canSelectThumbnailFromMedia = input<boolean>(true);

    readonly onMediaSelected = output<Media | null>();

    @ViewChild(MatMenuTrigger) matMenuTrigger!: MatMenuTrigger;

    readonly SvgIcon = SvgIcon;

    readonly media = model.required<Media | null>();

    private readonly _bodyDragAndDropEventsService = inject(BodyDragAndDropEventsService);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _mediaService = inject(MediaService);
    private readonly _processingMediasService = inject(ProcessingMediasService);
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);

    readonly maxMediaSizeInMo = signal(50);
    readonly isDragging = signal(false);
    readonly areDragEventsEnable = signal(true);
    readonly job = signal<ImportJob | null>(null);
    readonly isProcessingMedia = signal<boolean>(false);

    readonly uploadMediaProgress = computed(() => {
        const job = this.job();
        if (job) {
            switch (job.status) {
                case ImportStatus.SCHEDULED:
                    return 0;
                case ImportStatus.UPLOADING:
                    return (job.progress ?? 0) * 100;
                case ImportStatus.PROCESSING:
                    return (job.progress ?? 0) * 100;
            }
        }
        return 0;
    });
    readonly mediaFormatAccepted: Signal<FileFormat[]> = computed(() =>
        this.acceptedMimeTypes()
            .map((mimeType) => getExtensionFromMimeType(mimeType))
            .filter(isNotNil)
    );
    readonly mediasForThumbnailList = computed(() => {
        const media = this.media();
        if (media) {
            return [
                {
                    id: media.id,
                    url: media.urls.thumbnail256Outside ?? media.urls.original,
                    dimensions: media.dimensions.thumbnail256Outside ?? media.dimensions.original,
                    transformData: media.transformData,
                    type: media.type,
                },
            ];
        }
        return [];
    });

    readonly allowedMediaSources = computed(() => {
        const sources = this.allowedSources();
        return {
            fromComputer: sources.includes(ImageUploaderSource.COMPUTER),
            fromGallery: sources.includes(ImageUploaderSource.GALLERY),
        };
    });

    constructor() {
        this._handleBodyDragEvents();
    }

    // ------- Events handlers : drag and drop ------- //

    private _handleBodyDragEvents(): void {
        this._bodyDragAndDropEventsService.dragEnter
            .pipe(
                filter(() => this.areDragEventsEnable()),
                filter(this._hasFile),
                takeUntilDestroyed()
            )
            .subscribe(this._onDragEnter);
        this._bodyDragAndDropEventsService.dragOver
            .pipe(
                filter(() => this.areDragEventsEnable()),
                filter(this._hasFile),
                takeUntilDestroyed()
            )
            .subscribe(this._onDragOver);
        this._bodyDragAndDropEventsService.dragLeave
            .pipe(
                filter(() => this.areDragEventsEnable()),
                filter(this._hasFile),
                takeUntilDestroyed()
            )
            .subscribe(this._onDragLeave);
        this._bodyDragAndDropEventsService.drop
            .pipe(
                filter(() => this.areDragEventsEnable()),
                filter(this._hasFile),
                takeUntilDestroyed()
            )
            .subscribe(this._onDrop);
    }

    private _hasFile(event: DragEvent): boolean {
        return (event.dataTransfer?.types ?? []).includes('Files');
    }

    private _onDragEnter = (): void => {
        this.isDragging.set(true);
    };

    private _onDragOver = (event: DragEvent): void => {
        // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/File_drag_and_drop#prevent_the_browsers_default_drag_behavior
        // Prevent default behavior (Prevent file from being opened)
        event.preventDefault();
    };

    private _onDragLeave = (event: DragEvent): void => {
        event.preventDefault();
        this.isDragging.set(false);
    };

    private _onDrop = (event: DragEvent): void => {
        // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/File_drag_and_drop#prevent_the_browsers_default_drag_behavior
        // Prevent default behavior (Prevent file from being opened)
        event.preventDefault();
        this.isDragging.set(false);
        for (const file of Array.from(event.dataTransfer?.files ?? [])) {
            if (!this.acceptedMimeTypes().includes(file.type as ImageMimeType)) {
                this._toastService.openErrorToast(this._translateService.instant('common.image-uploader.error.invalid-file-type'));
                continue;
            }
            this._importMediaFromFile(file);
        }
    };

    // ------- Events handlers : upload from file / gallery ------- //

    onImportFromFile(event: Event): void {
        if (!this.allowedMediaSources().fromComputer) {
            return;
        }
        const target = event.target as HTMLInputElement;
        const files = target.files as FileList;
        for (const file of Array.from(files)) {
            this._importMediaFromFile(file);
        }
    }

    private async _importMediaFromFile(file: File): Promise<void> {
        const restaurant = this.restaurant();
        if (!restaurant) {
            return;
        }
        const restaurantId = restaurant.id;
        const importJob: ImportJob = { file, status: ImportStatus.SCHEDULED, progress: null };
        this.job.set(importJob);

        this._mediaService
            .uploadV2({
                file,
                onProgress: (progress) => this._updateUploadProgress(importJob.file, progress),
                queryParams: { restaurantId: restaurantId },
            })
            .then((result: UploadV2Result) => {
                if (result.success) {
                    this._getMediaFromProcessingMedia(result.result.processingMediaId);
                } else {
                    this.job.set(null);
                }
            });
    }

    private _updateUploadProgress(file: File, progress: number): void {
        this._updateJob(file, (job: ImportJob): ImportJob | null => {
            if (job.status !== ImportStatus.UPLOADING) {
                this.isProcessingMedia.set(true);
                return null;
            }
            return {
                file: job.file,
                status: ImportStatus.UPLOADING,
                progress,
            };
        });
    }

    private _updateJob(file: File, updateFn: (job: ImportJob) => ImportJob | null): void {
        this.job.update((currentJob) => {
            if (!currentJob || currentJob.file !== file) {
                return null; // No job found for the given file
            }
            return updateFn(currentJob);
        });
    }

    private _getMediaFromProcessingMedia(processingMediaId: string): void {
        this._processingMediasService
            .waitUntilEnded(processingMediaId)
            .then((processingMedia) => {
                if (processingMedia.status === ProcessingMediaStatus.SUCCESS && processingMedia.mediaId) {
                    this._mediaService
                        .getMediumById(processingMedia.mediaId)
                        .pipe(takeUntilDestroyed(this._destroyRef))
                        .subscribe({
                            next: ({ data: media }) => {
                                this.media.set(media);
                                this.job.set(null);
                                this.onMediaSelected.emit(media);
                                this.isProcessingMedia.set(false);
                            },
                            error: (error) => {
                                console.error('Failed to get media from processing media:', error);
                                this.job.set(null);
                                this.isProcessingMedia.set(false);
                            },
                        });
                } else {
                    console.error('Failed to process media:', processingMedia);
                    this.isProcessingMedia.set(false);
                    this.job.set(null);
                }
            })
            .catch((error) => {
                console.error('Error while waiting for processing media:', error);
                this.isProcessingMedia.set(false);
                this.job.set(null);
            });
    }

    onImportMediaFromGallery(): void {
        const restaurant = this.restaurant();
        if (!restaurant || !this.allowedMediaSources().fromComputer) {
            return;
        }
        this._customDialogService
            .open(MediaPickerModalComponent, {
                width: '600px',
                data: {
                    restaurant: this.restaurant(),
                    multi: false,
                    filter: MediaPickerFilter.ONLY_IMAGE,
                    selectedMedias: [],
                    maxMedia: 1,
                },
            })
            .afterClosed()
            .subscribe({
                next: (medias: Media[] | false) => {
                    if (medias) {
                        this.media.set(medias[0]);
                        this.onMediaSelected.emit(medias[0]);
                    }
                },
                error: (err) => {
                    console.warn('err :>>', err);
                },
            });
    }

    onReuploadMedia(): void {
        this.matMenuTrigger.openMenu();
    }
}
