---
import Analytics from ':components/Analytics.astro';
import CallToActions from ':components/blocks/CallToActions.astro';
import Descriptions from ':components/blocks/Descriptions.astro';
import Faq from ':components/blocks/Faq.astro';
import Gallery from ':components/blocks/Gallery.astro';
import Head from ':components/blocks/Head.astro';
import Information from ':components/blocks/Information.astro';
import Reviews from ':components/blocks/Reviews.astro';
import SocialNetworks from ':components/blocks/SocialNetworks.astro';
import WhiteMark from ':components/blocks/WhiteMark.astro';
import type {
    GetStoreLocatorPagesDto,
    GetStoreLocatorStorePageDto,
} from '@malou-io/package-dto';

interface Props {
    store: GetStoreLocatorStorePageDto;
    urls: GetStoreLocatorPagesDto['urls'];
    stores: { name: string; relativePath: string }[];
    HeaderComponent: any;
    FooterComponent: any;
}

const { store, HeaderComponent, urls, FooterComponent, stores } =
    Astro.props as Props;
---

<!doctype html>
<html lang={store.lang}>
    <head>
        <Head headBlock={store.headBlock} />
    </head>

    <body class="bg-tertiary pb-24 md:pb-0">
        <HeaderComponent
            urls={urls}
            stores={stores}
            lang={store.lang}
            storeId={store.id}
        />

        {
            store.informationBlock && (
                <Information
                    information={store.informationBlock}
                    styles={store.styles}
                />
            )
        }

        {
            store.galleryBlock && (
                <Gallery
                    galleryBlock={store.galleryBlock}
                    styles={store.styles}
                />
            )
        }

        {
            store.reviewsBlock && (
                <Reviews
                    reviewsBlock={store.reviewsBlock}
                    styles={store.styles}
                />
            )
        }

        {
            store.socialNetworksBlock && (
                <SocialNetworks
                    socialNetworksBlock={store.socialNetworksBlock}
                    styles={store.styles}
                />
            )
        }

        {
            store.callToActionsBlock && (
                <CallToActions
                    callToActionsBlock={store.callToActionsBlock}
                    styles={store.styles}
                />
            )
        }

        {
            store.descriptionsBlock && (
                <Descriptions
                    descriptionsBlock={store.descriptionsBlock}
                    styles={store.styles}
                />
            )
        }

        {
            store.faqBlock && (
                <Faq faqBlock={store.faqBlock} styles={store.styles} />
            )
        }

        <FooterComponent stores={stores} lang={store.lang} urls={urls} />

        <Analytics headBlock={store.headBlock} />

        {store.shouldDisplayWhiteMark && <WhiteMark styles={store.styles} />}
    </body>
</html>
